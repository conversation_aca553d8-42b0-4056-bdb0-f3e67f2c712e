#!/usr/bin/env python3
"""
Test script to verify the email verification code fix
"""

import ast
import re

def check_email_verification_function():
    """Check if the email verification function is properly implemented"""
    print("🔍 Checking email verification function implementation...")
    
    with open('iX_hinge946new.py', 'r') as f:
        content = f.read()
    
    # Check if the function exists
    has_function = 'def enter_email_verification_code(' in content
    print(f"✅ Function exists: {has_function}")
    
    # Check if it uses the correct element locator
    correct_locator = 'Email verification code Text field' in content
    print(f"✅ Uses correct locator: {correct_locator}")
    
    # Check if it uses WebDriverWait
    uses_webdriverwait = 'WebDriverWait(driver, 15)' in content and 'enter_email_verification_code' in content
    print(f"✅ Uses WebDriverWait: {uses_webdriverwait}")
    
    # Check if it's called in the main process
    function_called = 'enter_email_verification_code(driver, verification_code)' in content
    print(f"✅ Function is called: {function_called}")
    
    # Extract the function to show its implementation
    function_match = re.search(r'def enter_email_verification_code.*?(?=def|\Z)', content, re.DOTALL)
    if function_match:
        function_code = function_match.group(0)
        print(f"\n📝 Function implementation:")
        print("=" * 50)
        print(function_code[:500] + "..." if len(function_code) > 500 else function_code)
        print("=" * 50)
    
    return has_function and correct_locator and uses_webdriverwait and function_called

def check_email_flow():
    """Check the complete email verification flow"""
    print("\n🔍 Checking complete email verification flow...")
    
    with open('iX_hinge946new.py', 'r') as f:
        content = f.read()
    
    # Check key components
    has_api_functions = all(func in content for func in [
        'get_email_balance()',
        'order_email()',
        'get_email_message(',
        'extract_verification_code_from_email('
    ])
    print(f"✅ Has all API functions: {has_api_functions}")
    
    # Check API configuration
    has_api_config = all(var in content for var in [
        'EMAIL_API_TOKEN',
        'EMAIL_API_BASE_URL',
        'EMAIL_SITE = \'hinge.co\'',
        'EMAIL_DOMAINS = \'gmail\''
    ])
    print(f"✅ Has API configuration: {has_api_config}")
    
    # Check email ordering in main flow
    orders_email_in_flow = 'email_id, api_email_address = order_email()' in content
    print(f"✅ Orders email in main flow: {orders_email_in_flow}")
    
    # Check email message retrieval
    gets_message = 'get_email_message(email_id' in content
    print(f"✅ Gets email message: {gets_message}")
    
    return has_api_functions and has_api_config and orders_email_in_flow and gets_message

def main():
    print("🧪 Testing Email Verification Fix")
    print("=" * 50)
    
    # Test 1: Check email verification function
    function_ok = check_email_verification_function()
    
    # Test 2: Check complete email flow
    flow_ok = check_email_flow()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    if function_ok:
        print("✅ Email verification function: FIXED")
        print("   - Function exists with correct locator")
        print("   - Uses WebDriverWait properly")
        print("   - Called in main process")
    else:
        print("❌ Email verification function: NEEDS WORK")
    
    if flow_ok:
        print("✅ Email API integration: COMPLETE")
        print("   - All API functions implemented")
        print("   - Proper configuration")
        print("   - Integrated in main flow")
    else:
        print("❌ Email API integration: NEEDS WORK")
    
    print("\n🎯 EXPECTED BEHAVIOR:")
    print("1. Script orders Gmail from anymessage.shop API")
    print("2. Uses Gmail address in Hinge registration")
    print("3. Waits for verification email from Hinge")
    print("4. Extracts 6-digit code from email HTML")
    print("5. Enters code in: //XCUIElementTypeOther[@name=\"Email verification code Text field\"]")
    print("6. Clicks Next button to continue")
    
    if function_ok and flow_ok:
        print("\n🎉 EMAIL VERIFICATION IS READY!")
    else:
        print("\n⚠️  Some issues need to be resolved")

if __name__ == "__main__":
    main()
