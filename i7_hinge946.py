from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
from appium.options.ios import XCUITestOptions
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hains
from selenium.webdriver.common.actions.interaction import POINTER_TOUCH
from selenium.webdriver.common.actions.pointer_input import PointerInput
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.common.exceptions import NoSuchElementException
from datetime import datetime, timedelta
from email.header import decode_header
import random
import imaplib
import email
import re
import os
import base64
import re
import requests
import time

def generate_container_name(test_number, location):
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return f"Test {test_number} ({location}) - {current_time}"

iterations_data = [
    {
        "containerName": generate_container_name(1, 'Canberra'),
        "GPSlocation": "Canberra",
        "VPNlocation": "Sydney",
        "girlName": "<PERSON>rina",
        "emailAddress": "<EMAIL>",
        "emailPassword": "xnjdpgxxS!7449",
        "minimumDate": "1970-01-01",
        "maximumDate": "1985-12-31",
        "hometown": "Canberra",
        "workplace": "Elite Fitness Studio",
        "jobtitle": "Personal Trainer",
        "college": "University of Canberra",
        "promptAnswer1": "Catch me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...having breakfast in bed every day 💪",
        "promptAnswer3": "A fun fact is that...aliens created the pyramids!"
    },
    {
        "containerName": generate_container_name(2, 'Gosford'),
        "GPSlocation": "Gosford",
        "VPNlocation": "Melbourne",
        "girlName": "Sophie",
        "emailAddress": "<EMAIL>",
        "emailPassword": "fkziysvdS!1488",
        "minimumDate": "1971-02-14",
        "maximumDate": "1985-10-22",
        "hometown": "Gosford",
        "workplace": "Luxury Gym",
        "jobtitle": "Yoga Instructor",
        "college": "University of Newcastle",
        "promptAnswer1": "Follow me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting your yoga class delivered 💪",
        "promptAnswer3": "A fun fact is that...the Great Barrier Reef has alien origins!"
    },
    {
        "containerName": generate_container_name(3, 'Devonport'),
        "GPSlocation": "Devonport",
        "VPNlocation": "Brisbane",
        "girlName": "Nina",
        "emailAddress": "<EMAIL>",
        "emailPassword": "vhchpefrA!8276",
        "minimumDate": "1970-06-17",
        "maximumDate": "1984-08-30",
        "hometown": "Devonport",
        "workplace": "Luxury Fitness Center",
        "jobtitle": "Yoga Instructor",
        "college": "University of Tasmania",
        "promptAnswer1": "Catch me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting a personal trainer 💪",
        "promptAnswer3": "A fun fact is that...aliens live in the Tasman Sea!"
    },
    {
        "containerName": generate_container_name(4, 'Melbourne'),
        "GPSlocation": "Melbourne",
        "VPNlocation": "Perth",
        "girlName": "Phia",
        "emailAddress": "<EMAIL>",
        "emailPassword": "doalogywY!4179",
        "minimumDate": "1972-11-09",
        "maximumDate": "1985-12-11",
        "hometown": "Melbourne",
        "workplace": "High-End Yoga Studio",
        "jobtitle": "Yoga Instructor",
        "college": "Monash University",
        "promptAnswer1": "Follow me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...having a personal yoga guru 💪",
        "promptAnswer3": "A fun fact is that...aliens built the Sydney Opera House!"
    },
    {
        "containerName": generate_container_name(5, 'Mount Gambier'),
        "GPSlocation": "Mount Gambier",
        "VPNlocation": "Brisbane",
        "girlName": "Sofia",
        "emailAddress": "<EMAIL>",
        "emailPassword": "mrgwuukyA!9724",
        "minimumDate": "1971-04-04",
        "maximumDate": "1984-07-20",
        "hometown": "Mount Gambier",
        "workplace": "High-End Fitness Center",
        "jobtitle": "Personal Trainer",
        "college": "Flinders University",
        "promptAnswer1": "Catch me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...having the best fitness advice on call 💪",
        "promptAnswer3": "A fun fact is that...aliens helped create the Nullarbor Plain!"
    },
    {
        "containerName": generate_container_name(6, 'Adelaide'),
        "GPSlocation": "Adelaide",
        "VPNlocation": "Sydney",
        "girlName": "Soph",
        "emailAddress": "<EMAIL>",
        "emailPassword": "wzmpucxdX!7241",
        "minimumDate": "1970-07-28",
        "maximumDate": "1983-09-02",
        "hometown": "Adelaide",
        "workplace": "High-End Yoga Studio",
        "jobtitle": "Yoga Instructor",
        "college": "University of South Australia",
        "promptAnswer1": "Find me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...having an endless yoga retreat 💪",
        "promptAnswer3": "A fun fact is that...aliens live in the Coober Pedy underground homes!"
    },
    {
        "containerName": generate_container_name(7, 'Perth'),
        "GPSlocation": "Perth",
        "VPNlocation": "Melbourne",
        "girlName": "Niva",
        "emailAddress": "<EMAIL>",
        "emailPassword": "zwrdoavbS!8788",
        "minimumDate": "1972-09-22",
        "maximumDate": "1985-05-18",
        "hometown": "Perth",
        "workplace": "Luxury Fitness Center",
        "jobtitle": "Personal Trainer",
        "college": "Curtin University",
        "promptAnswer1": "Follow me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting fit without leaving home 💪",
        "promptAnswer3": "A fun fact is that...aliens have been seen in the Outback!"
    },
    {
        "containerName": generate_container_name(8, 'Albany aus'),
        "GPSlocation": "Albany",
        "VPNlocation": "Brisbane",
        "girlName": "Irina",
        "emailAddress": "<EMAIL>",
        "emailPassword": "nqnitjfpY!2496",
        "minimumDate": "1970-12-15",
        "maximumDate": "1984-10-19",
        "hometown": "Albany",
        "workplace": "Elite Fitness Studio",
        "jobtitle": "Personal Trainer",
        "college": "Great Southern TAFE",
        "promptAnswer1": "Catch me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...having a personal chef and trainer 💪",
        "promptAnswer3": "A fun fact is that...aliens are hiding in the Bungle Bungle Range!"
    },
    {
        "containerName": generate_container_name(9, 'Darwin'),
        "GPSlocation": "Darwin",
        "VPNlocation": "Perth",
        "girlName": "Soph",
        "emailAddress": "<EMAIL>",
        "emailPassword": "kxplnozeX!4028",
        "minimumDate": "1971-08-06",
        "maximumDate": "1985-12-08",
        "hometown": "Darwin",
        "workplace": "Luxury Gym",
        "jobtitle": "Yoga Instructor",
        "college": "Charles Darwin University",
        "promptAnswer1": "Follow me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting fit and living stress-free 💪",
        "promptAnswer3": "A fun fact is that...aliens have been sighted near Uluru!"
    },
    {
        "containerName": generate_container_name(10, 'Gisborne'),
        "GPSlocation": "Gisborne",
        "VPNlocation": "Melbourne",
        "girlName": "Phia",
        "emailAddress": "<EMAIL>",
        "emailPassword": "iulfugkmY!4292",
        "minimumDate": "1970-03-10",
        "maximumDate": "1983-11-25",
        "hometown": "Gisborne",
        "workplace": "Luxury Fitness Center",
        "jobtitle": "Personal Trainer",
        "college": "Victoria University",
        "promptAnswer1": "Find me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting pampered every day 💪",
        "promptAnswer3": "A fun fact is that...aliens have hidden portals in Gisborne!"
    },
        {
        "containerName": generate_container_name(11, 'Dunedin'),
        "GPSlocation": "Dunedin",
        "VPNlocation": "Brisbane",
        "girlName": "Niva",
        "emailAddress": "<EMAIL>",
        "emailPassword": "nvmcohesA!3844",
        "minimumDate": "1970-04-14",
        "maximumDate": "1984-09-18",
        "hometown": "Dunedin",
        "workplace": "Luxury Fitness Gym",
        "jobtitle": "Fitness Coach",
        "college": "University of Otago",
        "promptAnswer1": "Catch me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting your workout motivation daily 💪",
        "promptAnswer3": "A fun fact is that...the moon landing was staged!"
    },
    {
        "containerName": generate_container_name(12, 'Christchurch'),
        "GPSlocation": "Christchurch",
        "VPNlocation": "Sydney",
        "girlName": "So",
        "emailAddress": "<EMAIL>",
        "emailPassword": "vgahgtbuX!4615",
        "minimumDate": "1972-05-02",
        "maximumDate": "1985-07-16",
        "hometown": "Christchurch",
        "workplace": "Elite Fitness Studio",
        "jobtitle": "Personal Trainer",
        "college": "University of Canterbury",
        "promptAnswer1": "Follow me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting breakfast in bed every day 💪",
        "promptAnswer3": "A fun fact is that...aliens built the pyramids!"
    },
    {
        "containerName": generate_container_name(13, 'Wellington'),
        "GPSlocation": "Wellington",
        "VPNlocation": "Melbourne",
        "girlName": "Sophia",
        "emailAddress": "<EMAIL>",
        "emailPassword": "qqqlltxxX!6528",
        "minimumDate": "1970-09-19",
        "maximumDate": "1984-06-29",
        "hometown": "Wellington",
        "workplace": "Luxury Gym",
        "jobtitle": "Yoga Instructor",
        "college": "Victoria University of Wellington",
        "promptAnswer1": "Catch me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting personal training every day 💪",
        "promptAnswer3": "A fun fact is that...aliens are hiding in the ocean trenches!"
    },
    {
        "containerName": generate_container_name(14, 'Auckland'),
        "GPSlocation": "Auckland",
        "VPNlocation": "Perth",
        "girlName": "Phie",
        "emailAddress": "<EMAIL>",
        "emailPassword": "emrtbaqeS!2646",
        "minimumDate": "1973-08-21",
        "maximumDate": "1985-05-12",
        "hometown": "Auckland",
        "workplace": "High-End Fitness Center",
        "jobtitle": "Fitness Coach",
        "college": "University of Auckland",
        "promptAnswer1": "Find me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...having your own fitness retreat 💪",
        "promptAnswer3": "A fun fact is that...aliens control the weather!"
    },
    {
        "containerName": generate_container_name(15, 'Tulsa'),
        "GPSlocation": "Tulsa",
        "VPNlocation": "Houston",
        "girlName": "Sof",
        "emailAddress": "<EMAIL>",
        "emailPassword": "hfrkhlqjY!8635",
        "minimumDate": "1970-01-18",
        "maximumDate": "1985-10-03",
        "hometown": "Tulsa",
        "workplace": "Luxury Gym",
        "jobtitle": "Personal Trainer",
        "college": "University of Tulsa",
        "promptAnswer1": "Follow me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting your workout plan customized daily 💪",
        "promptAnswer3": "A fun fact is that...aliens are behind the Bermuda Triangle mysteries!"
    },
    {
        "containerName": generate_container_name(16, 'Memphis'),
        "GPSlocation": "Memphis",
        "VPNlocation": "Dallas",
        "girlName": "Riva",
        "emailAddress": "<EMAIL>",
        "emailPassword": "uotxxeixY!4300",
        "minimumDate": "1972-02-11",
        "maximumDate": "1984-11-07",
        "hometown": "Memphis",
        "workplace": "Luxury Fitness Studio",
        "jobtitle": "Yoga Instructor",
        "college": "University of Memphis",
        "promptAnswer1": "Catch me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting your daily workout motivation 💪",
        "promptAnswer3": "A fun fact is that...aliens are secretly living among us!"
    },
    {
        "containerName": generate_container_name(17, 'Dallas'),
        "GPSlocation": "Dallas",
        "VPNlocation": "Phoenix",
        "girlName": "Ina",
        "emailAddress": "<EMAIL>",
        "emailPassword": "pcumqyazY!1275",
        "minimumDate": "1971-06-09",
        "maximumDate": "1985-09-19",
        "hometown": "Dallas",
        "workplace": "Luxury Gym",
        "jobtitle": "Personal Trainer",
        "college": "University of Texas",
        "promptAnswer1": "Follow me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting fitness tips 24/7 💪",
        "promptAnswer3": "A fun fact is that...aliens have secret bases on the moon!"
    },
    {
        "containerName": generate_container_name(18, 'Houston'),
        "GPSlocation": "Houston",
        "VPNlocation": "Los Angeles",
        "girlName": "Nina",
        "emailAddress": "<EMAIL>",
        "emailPassword": "ehlepghmA!1435",
        "minimumDate": "1970-11-11",
        "maximumDate": "1984-07-01",
        "hometown": "Houston",
        "workplace": "High-End Fitness Studio",
        "jobtitle": "Personal Trainer",
        "college": "University of Houston",
        "promptAnswer1": "Catch me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...having a personal coach on speed dial 💪",
        "promptAnswer3": "A fun fact is that...aliens built Stonehenge!"
    },
    {
        "containerName": generate_container_name(19, 'San Antonio'),
        "GPSlocation": "San Antonio",
        "VPNlocation": "Miami",
        "girlName": "Soph",
        "emailAddress": "<EMAIL>",
        "emailPassword": "uiicjrqtS!7303",
        "minimumDate": "1970-04-15",
        "maximumDate": "1983-10-23",
        "hometown": "San Antonio",
        "workplace": "Luxury Fitness Center",
        "jobtitle": "Yoga Instructor",
        "college": "University of Texas at San Antonio",
        "promptAnswer1": "Follow me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...having your own yoga instructor 💪",
        "promptAnswer3": "A fun fact is that...aliens designed crop circles!"
    },
    {
        "containerName": generate_container_name(20, 'Baton Rouge'),
        "GPSlocation": "Baton Rouge",
        "VPNlocation": "Chicago",
        "girlName": "Phie",
        "emailAddress": "<EMAIL>",
        "emailPassword": "expjfghmX!2486",
        "minimumDate": "1970-09-07",
        "maximumDate": "1984-05-15",
        "hometown": "Baton Rouge",
        "workplace": "Elite Fitness Gym",
        "jobtitle": "Fitness Coach",
        "college": "Louisiana State University",
        "promptAnswer1": "Catch me on insta @strongSophieee 💪",
        "promptAnswer2": "Dating me is like...getting your fitness plan delivered 💪",
        "promptAnswer3": "A fun fact is that...aliens built the Great Wall of China!"
    }
]

# Appium desired capabilities setup using XCUITestOptions   
options = XCUITestOptions()
options.platformName = 'iOS'
options.platformVersion = '15.8'
options.deviceName = 'iPhone 7'
options.udid = '96188ab4ed6794359d6e644c76675a49263c8f6d'
options.automationName = 'XCUITest'
options.bundleId = 'co.hinge.mobile.ios'
options.xcodeOrgId = 'SHA.com'
options.xcodeSigningId = 'iPhone Developer'
options.set_capability('newCommandTimeout', 3000)

# Prevent WebDriverAgent from being reset or uninstalled
options.set_capability('noReset', True)

options.set_capability('useNewWDA', False)
options.set_capability('usePrebuiltWDA', True)

# Assign port 8102
options.set_capability('wdaLocalPort', 8102)

# Set up the Appium driver with host and port 127.0.0.1:4727
driver = webdriver.Remote('http://127.0.0.1:4727', options=options)

# # SMSpool API variables
# API_KEY = 'dwwpJOAHyo3T1P3T7DT4A8oE5B2wCU1n'
# COUNTRY_CODE = '1'  # country number from SMSpool API country list
# MAX_PRICE = '0.6'

# daisysms API variables
API_KEY = '7giTr2tqnWlmtHV6BpHfJugCZDLtSU'
COUNTRY_CODE = '55'  # country number from daisysms API country list
MAX_PRICE = '0.6'

def create_driver_session():
    driver = webdriver.Remote('http://127.0.0.1:4727', options=options)
    return driver

# Generate random number between a minimum and maximum
def generate_random_number(min_value, max_value):
    return random.randint(min_value, max_value)

# # Function to fetch the phone number from SMSpool API
# def get_phone_number():
#     url = 'https://api.smspool.net/purchase/sms'
#     headers = {
#         'Authorization': f'Bearer {API_KEY}'
#     }
#     data = {
#         'key': API_KEY,
#         'country': COUNTRY_CODE,
#         'service': 'Hinge',  # Makes sure the code gets returned well!
#         'max_price': MAX_PRICE,
#         'quantity': '1',
#         'pricing_option': '0'  # Cheapest option
#     }

#     response = requests.post(url, data=data, headers=headers)
#     response_data = response.json()

#     # Print the full response for debugging
#     print("API Response:", response_data)
    
#     # Extract phone number and order_id correctly
#     if response_data.get('success') == 1:
#         phone_number = response_data.get('phonenumber')  # Updated key
#         order_id = response_data.get('orderid')  # Updated key for order ID
#         print(f"Phone number purchased: {phone_number}")
#         return phone_number, order_id
#     else:
#         print("Failed to get phone number:", response_data.get('message', 'No message'))
#         return None, None

# # Function to poll for the SMS verification code
# def get_verification_code(order_id):
#     url = 'https://api.smspool.net/sms/check'
#     headers = {
#         'Authorization': f'Bearer {API_KEY}'
#     }
#     data = {
#         'key': API_KEY,
#         'orderid': order_id
#     }
#     # Polling until the SMS is received
#     for _ in range(10):  # Poll up to 10 times (adjust the number and delay as needed)
#         response = requests.post(url, data=data, headers=headers)
#         response_data = response.json()

#         # Print the full response for debugging
#         print("SMS Check Response:", response_data)
#         # driver.get_log('client')

#         # Fetch the SMS code from the correct key in the response
#         if 'sms' in response_data:
#             sms_code = response_data['sms']
#             print(f"Received SMS code: {sms_code}")
#             return sms_code
#         else:
#             print("No SMS yet. Retrying in 10 seconds...")
#             time.sleep(10)  # Wait for 10 seconds before retrying

#     print("Failed to receive SMS.")

#     driver.background_app(2)
    
#     didntgetcode = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Didn\'t get a code?"`]')
#     didntgetcode.click()
#     time.sleep(7.7)
#     sendagain = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send again"`]')
#     sendagain.click()
#     time.sleep(7.7)

#     # Polling until the SMS is received
#     for _ in range(10):  # Poll up to 10 times (adjust the number and delay as needed)
#         response = requests.post(url, data=data, headers=headers)
#         response_data = response.json()

#         # Print the full response for debugging
#         print("SMS Check Response:", response_data)

#         # Fetch the SMS code from the correct key in the response
#         if 'sms' in response_data:
#             sms_code = response_data['sms']
#             print(f"Received SMS code: {sms_code}")
#             return sms_code
#         else:
#             print("No SMS yet. Retrying in 10 seconds...")
#             time.sleep(10)  # Wait for 10 seconds before retrying
#     return None

# Function to fetch the phone number from Daisy API
def get_phone_number():
    url = 'https://daisysms.com/stubs/handler_api.php'
    data = {
        'api_key': API_KEY,  # Replace with your actual API key
        'action': 'getNumber',
        'service': 'vz',  # 'vz' for Hinge
        'max_price': 0.40  # Adjust max price as needed
    }

    response = requests.get(url, params=data)
    response_data = response.text.split(':')

    # Print the full response for debugging
    print("API Response:", response_data)

    if response_data[0] == 'ACCESS_NUMBER':
        order_id = response_data[1]
        phone_number = response_data[2]

        # Remove leading '1' from the phone number if it starts with '1'
        if phone_number.startswith('1'):
            phone_number = phone_number[1:]

        print(f"Phone number purchased: {phone_number}")
        return phone_number, order_id
    else:
        print("Failed to get phone number:", response_data[0])
        return None, None

# Function to poll for the SMS verification code
def get_verification_code(order_id):
    url = 'https://daisysms.com/stubs/handler_api.php'
    data = {
        'api_key': API_KEY,  # Replace with your actual API key
        'action': 'getStatus',
        'id': order_id
    }

    # Polling until the SMS is received
    for _ in range(10):  # Poll up to 10 times (adjust the number and delay as needed)
        response = requests.get(url, params=data)
        response_data = response.text.split(':')

        # Print the full response for debugging
        print("SMS Check Response:", response_data)

        if response_data[0] == 'STATUS_OK':
            sms_code = response_data[1]
            print(f"Received SMS code: {sms_code}")
            return sms_code
        elif response_data[0] == 'STATUS_WAIT_CODE':
            print("No SMS yet. Retrying in 10 seconds...")
            time.sleep(10)  # Wait for 10 seconds before retrying
        else:
            print(f"Failed to get SMS code: {response_data[0]}")
            break

    print("Failed to receive SMS.")
    # Optionally handle retries using Appium code here for resending the code
    return None

def enter_sms_code(driver, sms_code):
    # Ensure the code is 6 digits long
    if len(sms_code) != 6:
        print(f"Invalid SMS code length: {sms_code}")
        return
    
    # # back to home
    # driver.execute_script('mobile:pressButton', {"name": "home"})

    # time.sleep(2)

    # # Open Hinge app
    # el16 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Hinge"`]')
    # el16.click()

    # Enter each digit in the corresponding text field
    try:
        # First digit
        el1 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[1]/XCUIElementTypeTextField")
        el1.clear()
        el1.send_keys(sms_code[0])
        
        # Second digit
        el2 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeTextField")
        el2.clear()
        el2.send_keys(sms_code[1])
        
        # Third digit
        el3 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeTextField")
        el3.clear()
        el3.send_keys(sms_code[2])
        
        # Fourth digit
        el4 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeTextField")
        el4.clear()
        el4.send_keys(sms_code[3])
        
        # Fifth digit
        el5 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeTextField")
        el5.clear()
        el5.send_keys(sms_code[4])
        
        # Sixth digit
        el6 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[6]/XCUIElementTypeTextField")
        el6.clear()
        el6.send_keys(sms_code[5])

        print(f"SMS code {sms_code} entered successfully!")
        
    except Exception as e:
        print(f"Error during SMS code entry: {e}")


# Perform touch action using W3C actions
def perform_touch_action(driver, x, y, hold_duration=0.1):
    try:
        print(f"Performing touch action at ({x}, {y}) with hold duration of {hold_duration} seconds.")
        touch = PointerInput(POINTER_TOUCH, "touch")
        actions = ActionChains(driver)
        actions.w3c_actions.pointer_action.move_to_location(x, y)
        actions.w3c_actions.pointer_action.pointer_down()
        actions.w3c_actions.pointer_action.pause(hold_duration)
        actions.w3c_actions.pointer_action.pointer_up()
        actions.perform()
        print(f"Touch action completed at ({x}, {y}).")
    except Exception as e:
        print(f"Error during touch action: {e}")

# Tap and hold at a specific location
def tap_and_hold(driver, x, y, hold_duration=2):
    try:
        touch = PointerInput(POINTER_TOUCH, "touch")
        actions = ActionChains(driver)
        print(f"Tapping and holding at ({x}, {y}) for {hold_duration} seconds.")
        actions.w3c_actions.pointer_action.move_to_location(x, y)
        actions.w3c_actions.pointer_action.pointer_down()
        actions.w3c_actions.pointer_action.pause(hold_duration)
        actions.w3c_actions.pointer_action.pointer_up()
        actions.perform()
        print(f"Hold and release completed at ({x}, {y}).")
    except Exception as e:
        print(f"Error during tap and hold: {e}")

def random_date(min_date_str, max_date_str):
    # Convert strings to datetime objects
    min_date = datetime.strptime(min_date_str, "%Y-%m-%d")
    max_date = datetime.strptime(max_date_str, "%Y-%m-%d")

    # Generate a random number of days between the two dates
    delta = max_date - min_date
    random_days = random.randint(0, delta.days)

    # Add the random number of days to the minimum date and return it formatted as DD MM YYYY
    return (min_date + timedelta(days=random_days)).strftime("%m %d %Y")

def extract_verification_code_from_subject_and_body(subject, body):
    # Example function to extract the verification code (assuming it's a 6-digit code)
    import re
    # Try to extract from subject first
    match = re.search(r'\d{6}', subject)
    if match:
        return match.group(0)
    
    # If not found in subject, try the body
    match = re.search(r'\d{6}', body)
    if match:
        return match.group(0)
    
    return None

def get_verification_code_from_email(emailAddress, emailPassword, max_attempts=1, poll_delay=10):
    print(emailAddress)
    print(emailPassword)
    for attempt in range(max_attempts):  # Poll up to max_attempts times
        try:
            print(f"Connecting to IMAP server for {emailAddress}...")
            mail = imaplib.IMAP4_SSL("imap.firstmail.ltd", 993)  # Firstmail IMAP

            print(f"Logging in as {emailAddress}...")
            mail.login(emailAddress, emailPassword)
            print(f"Successfully logged in as {emailAddress}.")

            print(f"Selecting the inbox for {emailAddress}...")
            mail.select("inbox")

            print(f"Searching for emails with 'Verification' in the subject for {emailAddress}...")
            result, data = mail.search(None, '(SUBJECT "Verification")')

            if result != 'OK':
                print(f"Search failed with result: {result}")
                return None

            email_ids = data[0].split()

            if not email_ids:
                print(f"No verification emails found for {emailAddress}. Retrying in {poll_delay} seconds...")
                time.sleep(poll_delay)
                continue

            print(f"Found {len(email_ids)} email(s) for {emailAddress}. Fetching the latest one...")

            latest_email_id = email_ids[-1]
            result, msg_data = mail.fetch(latest_email_id, "(RFC822)")

            if result != 'OK':
                print(f"Failed to fetch email with result: {result}")
                return None

            # This is where the replacement block starts:
            for response_part in msg_data:
                if isinstance(response_part, tuple):
                    print(f"response_part: {response_part}")
                    print(f"type of response_part[1]: {type(response_part[1])}")

                    # If response_part[1] is not bytes, convert it
                    if isinstance(response_part[1], str):
                        response_part_bytes = response_part[1].encode('utf-8')
                        print("Converted response_part[1] to bytes")
                    else:
                        response_part_bytes = response_part[1]

                    # Now try parsing with message_from_bytes
                    try:
                        msg = email.message_from_bytes(response_part_bytes)
                        print("Email successfully parsed")
                    except Exception as e:
                        print(f"Error parsing email: {e}")
                        continue

                    email_subject = decode_header(msg["Subject"])[0][0]
                    if isinstance(email_subject, bytes):
                        email_subject = email_subject.decode()

                    print(f"Email subject: {email_subject}")

                    # Handle multipart emails
                    if msg.is_multipart():
                        for part in msg.walk():
                            if part.get_content_type() == "text/plain":
                                email_body = part.get_payload(decode=True).decode('utf-8', errors='replace')
                                print(f"Email body (text/plain): {email_body}")
                                break
                    else:
                        email_body = msg.get_payload(decode=True).decode('utf-8', errors='replace')
                        print(f"Email body: {email_body}")

                    # Extract verification code
                    verification_code_match = re.search(r'\b\d{6}\b', email_body)
                    if verification_code_match:
                        verification_code = verification_code_match.group(0)
                        print(f"Verification code found: {verification_code}")
                        return verification_code
                    else:
                        print("No verification code found")
                else:
                    print("Unexpected response format.")
            
        except Exception as e:
            print(f"Failed to retrieve email for {emailAddress}. Error: {e}")
            time.sleep(poll_delay)
            continue

        finally:
            print(f"Logging out of email for {emailAddress}.")
            mail.logout()

    print(f"Failed to receive verification code after {max_attempts} attempts.")
    return None

def enter_email_verification_code(driver, email_verification_code):
    try:
        # Ensure the email verification code is exactly 6 digits long
        if len(email_verification_code) != 6:
            print("Invalid email verification code length. Must be 6 digits.")
            return

        time.sleep(0.7)

        # Define both potential element path templates
        alternative_element_paths = [
            # First digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[1]/XCUIElementTypeTextField",
            # Second digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[2]/XCUIElementTypeTextField",
            # Third digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[3]/XCUIElementTypeTextField",
            # Fourth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[4]/XCUIElementTypeTextField",
            # Fifth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[5]/XCUIElementTypeTextField",
            # Sixth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[6]/XCUIElementTypeTextField"
        ]

        primary_element_paths = [
            # First digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[1]/XCUIElementTypeTextField",
            # Second digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[2]/XCUIElementTypeTextField",
            # Third digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[3]/XCUIElementTypeTextField",
            # Fourth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[4]/XCUIElementTypeTextField",
            # Fifth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[5]/XCUIElementTypeTextField",
            # Sixth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[6]/XCUIElementTypeTextField"
        ]

        # Function to attempt entering the code using given element paths
        def enter_code(paths):
            for index, path in enumerate(paths):
                el = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=path)
                el.clear()
                el.send_keys(email_verification_code[index])
                print(f"Entered digit {email_verification_code[index]} in field {index + 1}")

        # First attempt with primary paths
        try:
            enter_code(primary_element_paths)
            print("Clicking the Next button...")
            next_button = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
            next_button.click()
            print("Email verification code entered successfully using primary paths!")
        except Exception as primary_error:
            print(f"Primary paths failed: {primary_error}")

        # Try alternative paths if primary paths failed
        try:
            enter_code(alternative_element_paths)
            print("Clicking the Next button...")
            next_button = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
            next_button.click()
            print("Email verification code entered successfully using alternative paths!")
        except Exception as alternative_error:
            print(f"Both primary and alternative paths failed: {alternative_error}")
    
    except Exception as e:
        print(f"Error entering email verification code: {e}")

def retrieve_and_enter_verification_code(driver, email_address, email_password, max_attempts=3, delay_between_attempts=10):
    attempts = 0
    code_resent = False  # Track if the code has been resent

    while attempts < max_attempts:
        # Check if the session is still active
        if driver.session_id is None:
            print("Session terminated. Reinitializing the driver session...")
            driver = create_driver_session()  # Replace with your session initialization code
            driver.activate_app("co.hinge.mobile.ios")  # Reopen the app

        # Try to get the verification code from the email
        print(f"Attempt {attempts + 1}/{max_attempts}: Checking for verification code...")
        verification_code = get_verification_code_from_email(email_address, email_password, max_attempts=1)

        if verification_code:
            print(f"Final Verification Code for {email_address}: {verification_code}")
            enter_email_verification_code(driver, verification_code)

            # Click the 'Next' button to proceed
            try:
                print("Clicking the Next button...")
                next_button = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                next_button.click()

                # Wait for 4 seconds after clicking 'Next' for the Date Input Field to appear
                print("Waiting for 15 seconds to check for the Date Input Field...")
                time.sleep(15)

                # Try to find the 'Date Input Field'
                date_input_field = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Date Input Field"`]')
                print("Date Input Field found. Proceeding...")
                return True  # Exit the function after successful verification and transition

            except Exception as e:
                print(f"Date Input Field not found or couldn't click Next. Restarting the app. Error: {e}")

                # Restart the app and session
                try:
                    driver.terminate_app('co.hinge.mobile.ios')
                    driver.activate_app('co.hinge.mobile.ios')
                    time.sleep(15)
                except Exception as e:
                    print(f"Error during app restart: {e}")
                    driver.quit()  # Terminate the session
                    driver = create_driver_session()  # Reinitialize the session
                    driver.activate_app("co.hinge.mobile.ios")  # Reopen the app

                print("App reopened. Re-entering email and verification code...")

                try:
                    # filling in the email account
                    el23 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "<EMAIL>"`]')
                    el23.send_keys(email_address)

                    time.sleep(2.7)

                    el24 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                    el24.click()

                    time.sleep(7.7)

                    #### LOGIN WITH OTHER EMAIL WORKFLOW

                    # Clicking "No Thanks" instead of logging in somewhere
                    el25 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "No thanks"`]')
                    el25.click()

                except Exception:
                    pass

                time.sleep(7.7)

                # el24 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                # el24.click()

                # time.sleep(7.7)
                print("Re-entering the verification code...")
                enter_email_verification_code(driver, verification_code)  # Re-enter the code

        else:
            print(f"Failed to retrieve verification code for {email_address}, attempt {attempts + 1}/{max_attempts}")

        # Wait before attempting again
        time.sleep(delay_between_attempts)
        attempts += 1

        # If attempts exceed max_attempts and code hasn't been resent, trigger the resend code logic
        if attempts >= max_attempts and not code_resent:
            print("Attempting to resend verification code after the maximum failed attempts.")
            try:
                trouble_verifying = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Having trouble verifying?"`]')
                trouble_verifying.click()
                print("Clicked on 'Having trouble verifying?'")

                time.sleep(7.7)

                resend_code = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send code again"`]')
                resend_code.click()
                print("Clicked on 'Send code again'")

                print("Verification code resent.")
                code_resent = True  # Mark that the code has been resent

                # Reset attempts to restart the process after resending the code
                attempts = 0
                print("Restarting verification code checking process after resending code.")

            except Exception as e:
                print(f"Couldn't resend the code: {e}")
                return False

    print(f"Verification code not retrieved after {max_attempts} attempts.")
    return False


def click_next_and_reopen_if_needed(driver, verification_code, emailAddress):
    # Try clicking "Next" button
    try:
        el39 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el39.click()
        time.sleep(7.7)  # Wait to see if the next screen loads

        # Check if the expected element on the next screen is present
        next_screen_element = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Date Input Field"`]')
        if next_screen_element:
            print("Next screen loaded successfully.")
            return True

    except Exception as e:
        print("Failed to find or click 'Next' button or load the next screen:", e)

    # If we reach here, it means "Next" didn't work, so fully close and reopen the app
    print("Closing and reopening the app due to failure to advance...")

    # Fully close and relaunch the app
    driver.close_app()
    time.sleep(2)  # Small delay to ensure the app fully closes
    driver.launch_app()
    time.sleep(7)  # Wait for app to relaunch fully

    # Resend the code without fetching again (using the already fetched `verification_code`)
    try:
        print(f"Resending verification code: {verification_code}")
        emailfield = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "<EMAIL>"`]')
        emailfield.send_keys(emailAddress)
        time.sleep(0.77)
        emailnext = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        emailnext.click()
        time.sleep(2.8)
        enter_email_verification_code(driver, verification_code)
        time.sleep(2)  # Give time for verification code to be processed
    except Exception as e:
        print("Failed to re-enter the verification code after reopening the app:", e)

    return False

def tap_random_times(driver, x, y, hold_duration=0.2):
    # Randomly choose between 1, 2, or 3 taps
    tap_count = random.choice([1, 2, 3])
    print(f"Tapping and holding {tap_count} time(s).")
    
    # Perform the tap and hold action the chosen number of times
    for _ in range(tap_count):
        tap_and_hold(driver, x, y, hold_duration)

def perform_randomized_image_selection(driver):
    # Define the tap actions (coordinates for selecting images) in a list
    image_selection_actions = [
        (72, 175, 0.2),
        (194, 182, 0.2),
        (335, 173, 0.2),
        (66, 304, 0.2),
        (208, 299, 0.2),
        (313, 306, 0.2)
    ]

    # Shuffle the actions to randomize the order
    random.shuffle(image_selection_actions)

    # Perform each tap action in the randomized order
    for x, y, hold_duration in image_selection_actions:
        tap_and_hold(driver, x, y, hold_duration)
        time.sleep(0.9)

def perform_randomized_image_selection4deletion(driver):
    # Define the tap actions (coordinates for selecting images) in a list
    image_selection_actions = [
        (61, 429, 0.2),
        (188, 436, 0.2),
        (328, 419, 0.2),
        (60, 530, 0.2),
        (189, 520, 0.2),
        (313, 538, 0.2)
    ]

    # Shuffle the actions to randomize the order
    random.shuffle(image_selection_actions)

    # Perform each tap action in the randomized order
    for x, y, hold_duration in image_selection_actions:
        tap_and_hold(driver, x, y, hold_duration)
        time.sleep(0.9)

def find_correct_sliders(driver):
    # Loop through possible values for both minimum and maximum between 18 and 38
    for min_value in range(18, 39):  # Values from 18 to 38
        for max_value in range(18, 39):
            try:
                # Try finding the minimum slider
                el_min = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeSlider[`value == "minimum: {min_value}"`]')
                print(f"Found the minimum slider with value: {min_value}")
                
                # Try finding the maximum slider
                el_max = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeSlider[`value == "maximum: {max_value}"`]')
                print(f"Found the maximum slider with value: {max_value}")

                # If both are found, return them
                return el_min, el_max

            except Exception as e:
                # If it doesn't find the slider, continue searching
                print(f"Combination {min_value} and {max_value} did not match. Trying next...")

    # If no matching sliders are found, raise an error
    raise Exception("No valid sliders found in the range 18 to 38.")

def scroll_down(driver):

    # Calculate swipe start and end points (middle of the screen horizontally)
    start_x = 234
    start_y = 462  
    end_y = 172    # End near the top

    # Perform the scroll using 'mobile: swipe' for iOS
    driver.execute_script('mobile: swipe', {
        'direction': 'up',
        'startX': start_x,
        'startY': start_y,
        'endX': start_x,
        'endY': end_y,
        'duration': 800  # Duration in milliseconds
    })


def scroll_and_find_element(driver, element_locator, max_scrolls=10):
    scroll_attempts = 0

    while scroll_attempts < max_scrolls:
        try:
            # Try to find the element using its locator
            element = driver.find_element(AppiumBy.IOS_CLASS_CHAIN, element_locator)
            
            # If found, click the element
            element.click()
            print("Element found and clicked.")
            return True

        except NoSuchElementException:
            # If the element is not found, scroll down
            print(f"Element not found. Scrolling down... Attempt {scroll_attempts + 1}/{max_scrolls}")
            scroll_down(driver)
            scroll_attempts += 1

    print("Element not found after maximum scroll attempts.")
    return False

# THE PROCESS
def main_process(driver, variables):
    try:
        # Extract variables from the dictionary
        containerName = variables["containerName"]
        GPSlocation = variables["GPSlocation"]
        VPNlocation = variables["VPNlocation"]
        girlName = variables["girlName"]
        emailAddress = variables["emailAddress"]
        emailPassword = variables["emailPassword"]
        minimumDate = variables["minimumDate"]
        maximumDate = variables["maximumDate"]
        hometown = variables["hometown"]
        workplace = variables["workplace"]
        jobtitle = variables["jobtitle"]
        college = variables["college"]
        promptAnswer1 = variables["promptAnswer1"]
        promptAnswer2 = variables["promptAnswer2"]
        promptAnswer3 = variables["promptAnswer3"]

        driver = webdriver.Remote('http://127.0.0.1:4727', options=options)

#         driver.execute_script('mobile:pressButton', {"name": "home"})

#         time.sleep(4)

#         ## IN CASE OF IMAGE SPOOFING
#         # Open Photos and delete latest 6 images
#         el97 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Photos"`]')
#         el97.click()

#         driver.terminate_app('com.apple.mobileslideshow')
#         driver.activate_app('com.apple.mobileslideshow')

#         time.sleep(1.78)
#         try:
#             el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Albums"`]')
#             el98.click()
#             time.sleep(2.78)
#         except Exception as e:
#             pass

#         try:
#             el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Recents"`]')
#             el98.click()
#         except Exception as e:
#             pass

#         time.sleep(1.78)
#         try:
#             el99 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Select"`]')
#             el99.click()
#             if not el99:
#                 print("gay shit wuth pic deletion")
#                 return
#         except Exception as e:
#             el99 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Select"`]')
#             el99.click()
#             if not el99:
#                 print("gay shit wuth pic deletion")
#                 return

#         time.sleep(2.78)
#         perform_randomized_image_selection4deletion(driver)
#         time.sleep(2.7)
#         el100 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Delete"`]')
#         el100.click()
#         time.sleep(3.78)
#         el101 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Delete 6 Photos"`]')
#         el101.click()
#         time.sleep(2.77)
#         driver.execute_script('mobile:pressButton', {"name": "home"})

#         time.sleep(2.77)

#         # Tap and hold on the Hinge app (put standardly at right bottom pinned) and create new crane container
#         tap_and_hold(driver, 319, 623, hold_duration=2)
#         time.sleep(1)
#         tap_and_hold(driver, 280, 550, hold_duration=0.2)
#         time.sleep(2)
#         scroll_and_find_element(driver, '**/XCUIElementTypeButton[`name == "New Container"`]', 10)
#         tap_and_hold(driver, 280, 491, hold_duration=0.2)
#         time.sleep(3)

#         # Checking for name field, filling in new container name and creating the container
#         try:
#             print("Filling in the container name.")
            
#             # Send input to a text field
#             el2 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeTextField")
#             el2.clear()
#             el2.send_keys(containerName)  #Container Name
            
#             # Click "Next" button
#             el3 = driver.find_element(by=AppiumBy.ACCESSIBILITY_ID, value="Create")
#             el3.click()

#             print("Finalized container creation.")

#         except Exception as e:
#             print(f"Error during interaction: {e}")

#         # Go home to be sure, open GPS Manager
#         driver.execute_script('mobile:pressButton', {"name": "home"})
#         time.sleep(2)
#         el4 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "GPS Manager"`]')
#         el4.click()

#         driver.background_app(2)

#         print('Filling in GPS location.')

#         # set location
#         time.sleep(5)
#         el5 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Hinge"`]')
#         el5.click()
#         time.sleep(2.7)
#         el6 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Address"`]')
#         el6.click()
#         time.sleep(2.7)
#         el7 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Search"`]')
#         el7.click()
#         time.sleep(2.7)
#         el10 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeSearchField[`name == "Search Maps"`]')
#         el10.clear()
#         el10.send_keys(GPSlocation)
#         time.sleep(2.7)
#         el10_5 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeTable/XCUIElementTypeCell/XCUIElementTypeOther[2]/XCUIElementTypeOther')
#         el10_5.click()
#         time.sleep(2)

#         el9_5 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Save"`]')
#         el9_5.click()

#         el9 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "GPS Manager"`]')
#         el9.click()

#         # back to home
#         driver.execute_script('mobile:pressButton', {"name": "home"})

#         time.sleep(3)

#         # open Mullvad to rotate IP
#         el12 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Mullvad VPN"`]')
#         el12.click()

#         time.sleep(2)

#         # Changing IP location
#         try:
#             el13 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "selectLocationButton"`]')
#             el13.click()

#             time.sleep(3)

#             print("Filling in the Mullvad location.")
            
#             # Send location to text field
#             tap_and_hold(driver, 96, 125, hold_duration=0.2)
#             el14 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Entry"`]')
#             el14.clear()
#             el14.send_keys(VPNlocation)  # Location Name
#             time.sleep(3)

#             # Select location
#             el15 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeCell[`name == "cityLocationCell"`]/XCUIElementTypeOther[1]')
#             el15.click()

#             time.sleep(3)

#         except Exception as e:
#             print(f"Error during interaction: {e}")
            
#         time.sleep(3)

#         # back to home
#         driver.execute_script('mobile:pressButton', {"name": "home"})

#         time.sleep(4)

#         # Open Hinge app
#         el16 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Hinge"`]')
#         el16.click()

#         time.sleep(12)

#         # Step 2: Tap "Create account" button
#         el17 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Create account"`]')
#         el17.click()

#         time.sleep(2)
        
#         # countryflag = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "+31"`]')
#         # countryflag.click()
        
#         # scroll_and_find_element(driver, '**/XCUIElementTypeStaticText[`name == "United States"`]')
        
#         # targetcountry = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Spain"`]')
#         # targetcountry.click()

#         try:
#             # Step 1: Fetch phone number and order ID from sms API
#             phone_number, orderid = get_phone_number()
#             if phone_number is None or orderid is None:
#                 print("Could not fetch phone number. Exiting process.")
#                 return

#             time.sleep(5.7)

#             # Step 3: Fill the phone number fetched from the API
#             el18 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[1]/XCUIElementTypeOther/XCUIElementTypeOther[1]/XCUIElementTypeTextField")
#             el18.clear()
#             el18.send_keys(phone_number)
#             print(f"Phone number {phone_number} entered successfully!")
            
#             try:
#                 el19 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
#                 el19.click()
#                 print("Next button clicked successfully!")
#             except Exception as e:
#                 print(f"Failed to click the Next button. Error: {e}")
#                 print("Terminating the process due to too many verification requests.")
#                 return  # Terminate the process

#         except Exception as e:
#             print(f"Error during process: {e}")

#         time.sleep(5.7)

#         # Step 4: Poll for the SMS code from sms API
#         sms_code = get_verification_code(orderid)
#         if sms_code is None:
#             print("Could not fetch SMS code. Sending again.")
#             didntgetcode = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Didn\'t get a code?"`]')
#             didntgetcode.click()
#             time.sleep(7.7)
#             sendagain = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send again"`]')
#             sendagain.click()

#         time.sleep(0.7)

#         # # Open Hinge app
#         # el16 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Hinge"`]')
#         # el16.click()

#         # driver.background_app(2)
#         # driver.activate_app('co.hinge.mobile.ios')
        
#         # Step 5: Enter the SMS code in 6 separate fields
#         enter_sms_code(driver, sms_code)

#         try:
#             alreadyused = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Enter email code"`]')
#             if alreadyused:
#                 return
#         except Exception:
#             pass

#         time.sleep(1)
#         el19 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
#         el19.click()

#         # Clicking next to enter basic info
#         time.sleep(20.7)
#         try:
#             el20 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Enter basic info"`]')
#             el20.click()
#         except Exception as e:
#             el20 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Enter basic info"`]')
#             el20.click()

#         time.sleep(7.7)

#         el21 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "First name (required)"`]')
#         el21.send_keys(girlName) 

#         time.sleep(4.2)

#         el22 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
#         el22.click()

#         time.sleep(5.4)

#         # filling in the email account
#         el23 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "<EMAIL>"`]')
#         el23.send_keys(emailAddress)

#         time.sleep(2.7)

#         el24 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
#         el24.click()

#         time.sleep(7.7)

#         #### LOGIN WITH OTHER EMAIL WORKFLOW

#         # Clicking "No Thanks" instead of logging in somewhere
#         el25 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "No thanks"`]')
#         el25.click()


#         # ### LOGIN WITH GOOGLE WORKFLOW

#         # # Clicking "Login with Google"
#         # el25 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Continue with Google"`]')
#         # el25.click()
        
#         # time.sleep(0.9)

#         # try:
#         #     el26 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Continue"`]')
#         #     el26.click()
#         # except Exception as e:
#         #     print('Oke dan')

#         # time.sleep(1.3)

#         # # click email fill box, fill email and hit enter
#         # tap_and_hold(driver, 170, 387, hold_duration=0.2)
#         # el27 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Email verification helps us keep your account secure. Learn more"`]')
#         # el27.send_keys('<EMAIL>' + Keys.RETURN)

#         # time.sleep(5)

#         # # fill in password
#         # tap_and_hold(driver, 182, 430, hold_duration=0.23)
#         # el28 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Email verification helps us keep your account secure. Learn more"`]')
#         # el28.send_keys('yf4ssSalLE' + Keys.RETURN)

#         # time.sleep(4)

#         # el29 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Continue"`]')
#         # el29.click()

#         # time.sleep(2.3)

#         # el30 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Done"`]')
#         # el30.click()

#         # ### ADD HERE ##### FETCHING VERIFICATION CODE USING BELOW EMAILS TESTING CODE 
#         # ### FILLING IN THE VERIFICATION CODE ON HINGE AND CLICKING NEXT

#         # Wait a while before fetching and entering verification code
#         time.sleep(7.7)

# #NEEDED?? IF DATE INPUT FIELD NOT FOUND MAYBE TRY THIS
#         # driver.background_app(0.7)
#         # driver.background_app(0.7)

        verification_code = retrieve_and_enter_verification_code(driver, emailAddress, emailPassword)

        if verification_code:
            time.sleep(7.7)
            click_next_and_reopen_if_needed(driver, verification_code, emailAddress)
            time.sleep(7.7)
        else:
            print("Verification code not retrieved.")
        

        # Fill in randomized date, using minimum date and maximum date per model
        el40 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Date Input Field"`]')
    
        el40.send_keys(random_date(minimumDate, maximumDate))

        el41 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el41.click()

        time.sleep(1.77)

        el42 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Confirm"`]')
        el42.click()

        time.sleep(7.7)

        # [IMPERFECT] tapping enable notifications and going next
        tap_and_hold(driver, 133, 245, hold_duration=0.2)

        el43 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el43.click()

        # Clicking "Add more details"
        time.sleep(7.7)
        el44 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Add more details"`]')
        el44.click()

        # Clicking "Go to current location" to ensure GPS location is taken and then next
        time.sleep(7.7)
        el45 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Go to current location"`]')
        el45.click()
        time.sleep(7.7)
        el46 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el46.click()

        # Skipping gay ahh pronouns
        time.sleep(7.7)
        el47 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el47.click()

        # Selecting Gender and next
        time.sleep(7.7)
        el48 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Woman"`]')
        el48.click()
        el49 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el49.click()

        # Selecting sexuality and next
        time.sleep(7.7)
        el50 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Straight"`]')
        el50.click()
        el51 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el51.click()

        # Selecting who you want to date and next
        time.sleep(7.7)
        el52 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Men"`]')
        el52.click()
        el53 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el53.click()

        # Selecting relationship type and next
        time.sleep(7.7)
        el54 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Monogamy"`]')
        el54.click()
        el55 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el55.click()

        # Selecting dating intention and next
        time.sleep(7.7)
        el56 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Figuring out my dating goals"`]')
        el56.click()
        el57 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el57.click()

        # Randomly change height and next
        time.sleep(7.7)
        tap_random_times(driver, 186, 370, hold_duration=0.2)
        el58 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el58.click()

        # Change ethnicity (not visible)
        time.sleep(7.7)
        el59 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "checked; Visible on profile"`][6]')
        el59.click()
        tap_and_hold(driver, 337, 263, hold_duration=0.2)
        el60 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el60.click()

        # Do you have children?
        time.sleep(7.7)
        tap_and_hold(driver, 337, 321, hold_duration=0.2)
        el61 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el61.click()

        # Family plans
        time.sleep(7.7)
        tap_and_hold(driver, 337, 359, hold_duration=0.2)
        el62 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el62.click()

        # # Hometown
        # time.sleep(7.7)
        # el63 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "Home town"`]')
        # el63.send_keys(hometown)
        el64 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el64.click()

        # Workplace
        time.sleep(7.7)
        el65 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "Workplace"`]')
        el65.send_keys(workplace)
        el66 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el66.click()

        # Job title
        time.sleep(7.7)
        el67 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "Job title"`]')
        el67.send_keys(jobtitle)
        el68 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el68.click()

        # # College or university
        # time.sleep(7.7)
        # el69 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "Add a college or university"`]')
        # el69.send_keys(college)
        el70 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el70.click()

        # Degree
        time.sleep(7.7)
        tap_and_hold(driver, 337, 303, hold_duration=0.2)
        el71 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el71.click()

        # Religious beliefs
        time.sleep(7.7)
        tap_and_hold(driver, 337, 471, hold_duration=0.2)
        el72 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el72.click()

        # Political beliefs
        time.sleep(7.7)
        tap_and_hold(driver, 337, 471, hold_duration=0.2)
        el73 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el73.click()

        # Do you drink?
        time.sleep(7.7)
        tap_and_hold(driver, 337, 262, hold_duration=0.2)
        el74 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el74.click()

        # Do you smoke tobacco?
        time.sleep(7.7)
        tap_and_hold(driver, 337, 320, hold_duration=0.2)
        el75 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el75.click()

        # Do you smoke weed?
        time.sleep(7.7)
        tap_and_hold(driver, 337, 320, hold_duration=0.2)
        el76 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el76.click()

        # Do you use drugs?
        time.sleep(7.7)
        tap_and_hold(driver, 337, 320, hold_duration=0.2)
        el77 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el77.click()
        time.sleep(7.7)

        # check for "We value your privacy" screen
        try:
            el877 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "I accept"`]')
            el877.click()
            el878 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
            el878.click()

        except Exception as e:
            print(f"No 'We value your privacy' screen")
            pass

        # Fill out your profile
        time.sleep(7.7)
        tap_and_hold(driver, 337, 320, hold_duration=0.2)
        el78 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Fill out your profile"`]')
        el78.click()

        # Add pictures
        time.sleep(2.7)
        el78 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeImage[`name == "mediaInputAddMedia4"`][1]')
        el78.click()
        time.sleep(1.77)
        el79 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Camera Roll"`]')
        el79.click()

        # Photos screen open, taps the first six images in a random order
        time.sleep(9.75)
        perform_randomized_image_selection(driver)

        # click "Add"
        el80 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Add"`]')
        el80.click()

        # click next on all the pics, then done, and next on the hinge interface
        time.sleep(7.7)
        for i in range(5):
            el81 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
            el81.click()
        el82 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]')
        el82.click()
        el83 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el83.click()

        time.sleep(23)
        try:
            # Try to find the button element with the given name
            print("Checking for the prompt screen...")
            missing_prompt_button = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Missing prompt. Select a Prompt and write your own answer."`][1]')
            print("'Prompt screen found, continuing with the process...")
            # Continue the rest of your method here
            pass  # This allows the function to continue

        except Exception as e:
            # If the element is not found, wait and retry or move on
            print(f"'Missing prompt' button not found. Waiting and retrying. Error: {e}")
            time.sleep(10)  # Wait for 4 seconds or as necessary

        # Prompts
        # Prompt no. 1
        el84 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Missing prompt. Select a Prompt and write your own answer."`][1]')
        el84.click()
        time.sleep(7.7)

        # click View All
        el777 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "View all"`]')
        el777.click()

        time.sleep(7.7)

        el778 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "All I ask is that you"`]')
        el778.click()
        time.sleep(2.7)
        tap_and_hold(driver, 149, 223, 0.2) # click the textfield for the prompt answer
        
        time.sleep(2.7)
        el85 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Be completely true to yourself."`]')
        el85.send_keys(promptAnswer1)
        time.sleep(2.7)
        el86 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]')
        el86.click()

        # Prompt no. 2
        time.sleep(2.7)
        el87 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Missing prompt. Select a Prompt and write your own answer."`][2]')
        el87.click()
        time.sleep(2.7)

        # click View All
        el777 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "View all"`]')
        el777.click()

        el779 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Dating me is like"`]')
        el779.click()
        tap_and_hold(driver, 149, 223, 0.2) # click the textfield for the prompt answer
        time.sleep(2.7)
        el88 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Seeing a D-list celebrity at the airport. Mildly thrilling."`]')
        el88.send_keys(promptAnswer2)
        time.sleep(2.7)
        el89 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]')
        el89.click()

        # Prompt no. 3
        time.sleep(2.7)
        el90 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Missing prompt. Select a Prompt and write your own answer."`]')
        el90.click()
        time.sleep(2.7)

        el777 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "View all"`]')
        el777.click()

        el780 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "A random fact I love is"`]')
        el780.click()
        tap_and_hold(driver, 149, 223, 0.2) # click the textfield for the prompt answer
        time.sleep(7.7)
        el91 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "It\'s illegal to own just one guinea pig in Switzerland because they get lonely."`]')
        el91.send_keys(promptAnswer3)
        time.sleep(7.7)
        el92 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]')
        el92.click()
        time.sleep(7.7)

        el93 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el93.click()
        time.sleep(7.7)

        el94 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el94.click()
        time.sleep(7.7)

        # skip the upsell screen and the "All Done" screen
        el95 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Maybe later"`]')
        el95.click()
        time.sleep(2.7)
        el96 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Start sending likes"`]')
        el96.click()

        # Let it loaadd
        time.sleep(14.7)
        tap_and_hold(driver, 222, 222, 0.2)

        # # Setting the right age range
        # # el97 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Age"`]')
        # # el97.click()
        # # tap_and_hold(driver, 127, 469, 0.1)
        # el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Age"`]')
        # el98.click()
        # time.sleep(7.7)


        # try:
        #     # Call the function to automatically find the correct slider elements
        #     el99, el100 = find_correct_sliders(driver)
    
        #     # Interact with the found sliders
        #     el99.send_keys('0.18')  # Adjust the minimum slider
        #     time.sleep(7.7)         # Wait for the action to complete
        #     el100.send_keys('0.88') # Adjust the maximum slider
        #     print("Successfully adjusted age preferences.")
    
        # except Exception as e:
        #     print(f"Error during slider interaction: {e}")


        # time.sleep(7.7)
        # el101 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Apply filter"`]')
        # el101.click()
        # time.sleep(7.7)

        # el102 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTabBar[`name == "Tab Bar"`]/XCUIElementTypeButton[5]')
        # el102.click()

        # time.sleep(7.7)

        # el103 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "dials medium"`]')
        # el103.click()

        # time.sleep(2.7)

        # el104 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Maximum distance"`]')
        # el104.click()

        # time.sleep(2.7)

        # el105 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeSlider[`value == "160 km"`]')
        # el105.send_keys(0.60)

        # time.sleep(7.7)
        
        # # go back
        # tap_and_hold(driver, 20, 42, 0.14)

        # time.sleep(7.7)

        # el1055 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "close"`]')
        # el1055.click()

        # time.sleep(7.7)

        # el106 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeImage[`name == "tabBarDiscoverDeselected"`]')
        # el106.click()

        # time.sleep(2.7)

        # # el1065 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "OK, got it"`]')
        # # el1065.click()

        # # time.sleep(2.7)

        # # el1066 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Not now"`]')
        # # el1066.click()

        # # time.sleep(2.7)

        # # el107 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Skip"`]')
        # # el107.click()

        # # time.sleep(2.7)

        # # el110 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Like"`][1]')
        # # el110.click()

        # # time.sleep(2.7)

        # # el108 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send Like"`]')
        # # el108.click()

        # # time.sleep(2.7)

        # # el1085 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send Like anyway"`]')
        # # el1085.click()

        # # try:
        # #     el109 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "close"`]')
        # #     el109.click()
        # # except Exception as e:
        # #     pass

        # # el110 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Like"`][1]')
        # # el110.click()

        # # time.sleep(2.7)

        # # el108 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send Like"`]')
        # # el108.click()

        # # time.sleep(2.7)

        # # el107 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Skip"`]')
        # # el107.click()

        # # time.sleep(2.7)

        # # el110 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Like"`][1]')
        # # el110.click()

        # # time.sleep(2.7)

        # # el108 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send Like"`]')
        # # el108.click()

        # # time.sleep(2.7)

        # # el107 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Skip"`]')
        # # el107.click()

        # # time.sleep(2.7)

        # # el110 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Like"`][1]')
        # # el110.click()

        # # time.sleep(3.3)

        # # el108 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send Like"`]')
        # # el108.click()

        # # time.sleep(2.7)

        # # el107 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Skip"`]')
        # # el107.click()

        # # time.sleep(2.7)

        # # el107 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Skip"`]')
        # # el107.click()

        # # time.sleep(2.7)

        driver.execute_script('mobile:pressButton', {"name": "home"})
        time.sleep(7.7)

        # ## IN CASE OF IMAGE SPOOFING
        # # Open Photos and delete latest 6 images
        # el97 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Photos"`]')
        # el97.click()
        # time.sleep(3.78)
        # try:
        #     el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Albums"`]')
        #     el98.click()
        #     time.sleep(3.78)
        # except Exception as e:
        #     pass

        # try:
        #     el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Recents"`]')
        #     el98.click()
        # except Exception as e:
        #     pass

        # time.sleep(3.78)
        # try:
        #     el99 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Select"`]')
        #     el99.click()
        #     if not el99:
        #         print("gay shit wuth pic deletion")
        #         return
        # except Exception as e:
        #     el99 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Select"`]')
        #     el99.click()
        #     if not el99:
        #         print("gay shit wuth pic deletion")
        #         return

        # time.sleep(5.78)
        # perform_randomized_image_selection4deletion(driver)
        # time.sleep(2.7)
        # el100 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Delete"`]')
        # el100.click()
        # time.sleep(3.78)
        # el101 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Delete 6 Photos"`]')
        # el101.click()
        # time.sleep(2.77)
        # driver.execute_script('mobile:pressButton', {"name": "home"})

    except Exception as e:
        print(f"Error during process for {containerName}: {e}")


# Loop through the iterations_data list and call the main_process with each set of variables
for i, variables in enumerate(iterations_data):
    print(f"Starting process for iteration {i + 1}")
    main_process(driver, variables)
    print(f"Completed process for iteration {i + 1}")

# End the session
# driver.quit()
