#!/usr/bin/env python3
"""
Test script to verify proxy rotation functionality
"""

import requests
import time

# Proxy rotation URL
PROXY_ROTATION_URL = 'https://i.fxdx.in/api-rt/changeip/RIDATjivGW/x65VKTSAP5WNP'

def test_proxy_rotation():
    """Test the proxy rotation functionality"""
    try:
        print("Testing proxy rotation...")
        print(f"Making request to: {PROXY_ROTATION_URL}")
        
        response = requests.get(PROXY_ROTATION_URL, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Text: {response.text}")
        
        if response.status_code == 200:
            print("✅ Proxy rotation test successful!")
        else:
            print("❌ Proxy rotation test failed!")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error during proxy rotation test: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_daisysms_api():
    """Test the DaisySMS API with the new key"""
    API_KEY = 'qtlLKjcGPVol7qoth68ASvPWtKUpR7'
    
    try:
        print("\nTesting DaisySMS API...")
        url = 'https://daisysms.com/stubs/handler_api.php'
        data = {
            'api_key': API_KEY,
            'action': 'getBalance'
        }
        
        response = requests.get(url, params=data, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            if 'ACCESS_BALANCE' in response.text:
                print("✅ DaisySMS API test successful!")
            else:
                print("❌ DaisySMS API test failed - unexpected response")
        else:
            print("❌ DaisySMS API test failed!")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error during DaisySMS API test: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🧪 Running tests for updated i7deux_hinge946.py")
    print("=" * 50)
    
    test_proxy_rotation()
    test_daisysms_api()
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")
    print("\nNext steps:")
    print("1. Implement Geranium coordinate input functionality")
    print("2. Provide coordinate mappings for all locations")
    print("3. Test the full Hinge automation process")
