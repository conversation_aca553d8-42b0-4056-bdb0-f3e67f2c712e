Rejected Emails:

Successful Emails:
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 1 (Quebec) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 2 (London CA) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 3 (Halifax) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 4 (Ottawa) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 5 (Sudbury) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 6 (Sioux-Lookout) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 7 (Winnipeg) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 8 (Regina) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 9 (Edmonton) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 10 (North Battleford) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 11 (Kelowna) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 12 (Vancouver) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 13 (Prince George) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 14 (Terrace Canada) - 2024-10-02 21:20:20
Email: <EMAIL>, Date Used: 2024-10-02 21:20:28, Iteration: Test 15 (St. John’s) - 2024-10-02 21:20:20
