from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
from appium.options.ios import XCUITestOptions
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.actions.interaction import POINTER_TOUCH
from selenium.webdriver.common.actions.pointer_input import PointerInput
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.common.exceptions import NoSuchElementException
from datetime import datetime, timedelta
from email.header import decode_header
import random, string
import imaplib
import email
import re
import requests
import time

def generate_container_name(test_number, location):
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return f"Test {test_number} ({location}) - {current_time}"

# Function to parse emails in the format email:password
def parse_emails(email_string):
    emails = []
    lines = email_string.strip().split("\n")
    for line in lines:
        email, password = line.split(":")
        emails.append({"emailAddress": email, "emailPassword": password})
    return emails

# Define girl name categories and corresponding Instagram handles
girl_names_and_handles = {
    "Irina": {
        "girlNames": ["<PERSON>", "In<PERSON>", "Irina", "<PERSON><PERSON>", "<PERSON>iri", "Ri", "Iva", "<PERSON>a", "Inabe<PERSON>"],
        "ig_handle": "@heyyitsirina"
    },
    "<PERSON>": {
        "girl<PERSON>ames": ["<PERSON>", "<PERSON>", "<PERSON>a", "<PERSON>", "So", "Sof", "Soph", "<PERSON>e", "<PERSON>ph"],
        "ig_handle": "@suedeesophiee"
    },
    "Hellena": {
        "girlNames": ["Hellen", "Lena", "Helen", "Helena", "Hella", "Leena", "Hel", "Helli"],
        "ig_handle": "@hellohellenaa"
    }
}

# Define GPS and VPN pairs
GPS_VPN_pairs = [
    {"GPSlocation": "adelaide", "countryshort": "au", "VPNlocation": "Adelaide"},
    {"GPSlocation": "goldcoast", "countryshort": "au", "VPNlocation": "Brisbane"},
    {"GPSlocation": "sandiego", "countryshort": "us", "VPNlocation": "Los Angeles"},
    {"GPSlocation": "dallas", "countryshort": "us", "VPNlocation": "Dallas"},
    {"GPSlocation": "sanjose", "countryshort": "us", "VPNlocation": "San Jose"},
    {"GPSlocation": "kitchener", "countryshort": "ca", "VPNlocation": "Toronto"},
    {"GPSlocation": "austin", "countryshort": "us", "VPNlocation": "Dallas"},
    {"GPSlocation": "jacksonville", "countryshort": "us", "VPNlocation": "Atlanta"},
    {"GPSlocation": "fortworth", "countryshort": "us", "VPNlocation": "Dallas"},
    {"GPSlocation": "columbus", "countryshort": "us", "VPNlocation": "Chicago"},
    {"GPSlocation": "charlotte", "countryshort": "us", "VPNlocation": "Raleigh"},
    {"GPSlocation": "sanfrancisco", "countryshort": "us", "VPNlocation": "San Jose"},
    {"GPSlocation": "hamilton", "countryshort": "ca", "VPNlocation": "Toronto"}
]

# Input email list (email:password format)
email_input = """
<EMAIL>:DLhvtVPiyJ
<EMAIL>:u0DMgat7kt
<EMAIL>:BfcOJGKisg
<EMAIL>:LyGifPIIvo
<EMAIL>:QMKNa1sZBr
<EMAIL>:3sos3UJHPi
<EMAIL>:bmpXsbSH3r
<EMAIL>:Baxab7lp6C
<EMAIL>:ZGAvMPg7OC
<EMAIL>:dpeMdm9J01
<EMAIL>:EExFR41e5W
<EMAIL>:LysxIXXZ8U
"""

# Parse the email input
emails = parse_emails(email_input)

# Choose girl category
selected_category = "Irina"  # Change this to other model if needed

# Get girl names and Instagram handle for the selected category
girl_names = girl_names_and_handles[selected_category]["girlNames"]
ig_handle = girl_names_and_handles[selected_category]["ig_handle"]

# Check if there are enough GPS/VPN pairs for the emails
if len(emails) > len(GPS_VPN_pairs):
    raise ValueError("Not enough GPS and VPN pairs for the number of emails.")

# Generate iterations dynamically
iterations_data = []
for i in range(len(emails)):
    addy = emails[i]
    gps_vpn_pair = GPS_VPN_pairs[i]
    girl_name = random.choice(girl_names)
    
    iteration = {
        "containerName": generate_container_name(i + 1, gps_vpn_pair["GPSlocation"]),
        "GPSlocation": gps_vpn_pair["GPSlocation"],
        "VPNlocation": gps_vpn_pair["VPNlocation"],
        "countryshort": gps_vpn_pair["countryshort"],
        "girlName": girl_name,
        "emailAddress": addy["emailAddress"],
        "emailPassword": addy["emailPassword"],
        "minimumDate": "1975-01-01",  
        "maximumDate": "2000-12-31", 
        "hometown": gps_vpn_pair["GPSlocation"],
        "workplace": f"{random.choice(['Elite', 'Luxury', 'Premium'])} Hairdressers",
        "jobtitle": random.choice(["Colourist", "Manager", "Affiliate", "PA", "Stylist"]),
        "college": f"{gps_vpn_pair['GPSlocation']} University",
        "promptAnswer1": random.choice([f"find my hidden insta on this pagee 😏",
            f"go look for my ig handle hereee 😝", f"take on the challenge of finding my hidden ig handle",
            f"find my hidden ig username here.. do you dare?", f"find my ig username (hidden 😳)"]),
        "promptAnswer2": random.choice([
        "midnight adventuressss 👹", "a lot of hair, wont even lie haha", "colourssss",
            "your favourite rollercoaster", "minecraft", "that secret level in your favorite video game 🎮", 
            "a cozy blanket fort with fairy lights", "a chaotic good energy boost 😝🔋", 
            "a rainy night movie marathon ☔️", "a never-ending Sunday brunch 🥞", 
            "unpacking a rare Pokémon card", "an all-access backstage pass", 
            "living in a Studio Ghibli movie 🍃", "a midnight snack raid 🌙", 
            "the best surprise road trip 🚗", "a festival headliner you can’t stop talking about 🎸", 
            "a mysterious new flavor of ice cream 🍦", "that song you keep replaying", 
            "owning a collection of too many cozy sweaters", "getting lost in a vintage bookstore 📚", 
            "a scavenger hunt in the city", "a slow dance under string lights", 
            "a really good mystery novel you can’t put down", "that one friend who’s always up for spontaneous plans", 
            "a two-player game with infinite levels", "your favorite meme page in real life", 
            "a cliffhanger season finale 📺", "an unexpected but perfect movie plot twist", 
            "the best coffee shop on a rainy day ☕", "your favorite childhood theme park", 
            "finding hidden treasures in a thrift store", "a rollercoaster but with safety belts 🛡️", 
            "an all-you-can-eat sushi night", "a classic with a remix twist", 
            "a sun-soaked road trip playlist ☀️", "a colorful mural in a quiet alley 🎨", 
            "finding that perfect vinyl record", "a spontaneous midnight ice cream run 🍨", 
            "unraveling the mysteries of the universe together", "a new season of your comfort series", 
            "an unplanned dance party", "one too many movie quotes", "an unpredictable weather forecast", 
            "an unexplored city at night 🌆", "a jigsaw puzzle with a missing piece"]), # type: ignore
        "promptAnswer3": random.choice([
        "otters sleep while holding hands so they dont drift away :)",
            "my apples are interestingly prominent.",
            "there are more frog species than car brands",
            "i have proper apples (you interpret that (good luck with it))",
            "the angry German guy is still alive",
            "honey never spoils – they found pots of it in ancient Egyptian tombs!",
            "bananas are berries, but strawberries aren’t 🍓",
            "you can’t hum while holding your nose (try it!)",
            "sea otters have a favorite rock they keep in a pouch under their arms 🦦",
            "octopuses have three hearts ❤️",
            "a group of flamingos is called a ‘flamboyance’ 🌸",
            "the Eiffel Tower grows up to 6 inches in summer due to heat expansion",
            "kangaroos can’t walk backward",
            "butterflies taste with their feet",
            "a day on Venus is longer than a year on Venus",
            "some cats are allergic to humans 😹",
            "sloths can take up to a month to digest one meal",
            "a crocodile can’t stick its tongue out",
            "polar bears have black skin under their white fur 🐻‍❄️",
            "pineapples take about two years to grow 🍍",
            "wombat poop is cube-shaped",
            "koalas sleep up to 22 hours a day 😴",
            "chewing gum was once made from tree sap",
            "rabbits can’t vomit",
            "only female mosquitoes bite",
            "ants don’t have lungs, they breathe through tiny holes in their bodies",
            "the unicorn is the national animal of Scotland 🦄",
            "there’s a species of jellyfish that is technically immortal",
            "frogs use their eyes to help swallow food",
            "squirrels plant thousands of new trees each year by forgetting where they buried their acorns",
            "there’s a basketball court in the U.S. Supreme Court building 🏀",
            "your heartbeat syncs to the rhythm of the music you listen to 🎶",
            "a shrimp’s heart is located in its head",
            "a snail can sleep for three years 🐌",
            "the inventor of the Pringles can is now buried in one"]) # type: ignore
        } 
    
    iterations_data.append(iteration)

# Appium desired capabilities setup using XCUITestOptions   
options = XCUITestOptions()
options.platformName = 'iOS'
options.platformVersion = '15.7.7'
options.deviceName = 'iPhone 7'
options.udid = '96188ab4ed6794359d6e644c76675a49263c8f6d'
options.automationName = 'XCUITest'
options.bundleId = 'com.moxco.bumble.ios'
options.xcodeOrgId = 'SHA.com'
options.xcodeSigningId = 'iPhone Developer'
options.set_capability('newCommandTimeout', 120000)
options.set_capability('wdaConnectionTimeout', 120000) 
options.set_capability("appium:wdaLaunchTimeout", 180000) 

# Prevent WebDriverAgent from being reset or uninstalled
options.set_capability('noReset', True)
options.set_capability('useNewWDA', False)
options.set_capability('usePrebuiltWDA', True)

# Assign port 8108
options.set_capability('wdaLocalPort', 8108)

# Set up the Appium driver with host and port 127.0.0.1:4727
driver = webdriver.Remote('http://127.0.0.1:4727', options=options)

# SMSpool API variables
API_KEY = 'rOG0l2YQ8Mar2yGTbPOkREpHtdf86SL9'
COUNTRY_CODE = '2'  # country number from SMSpool API country list
MAX_PRICE = '0.18'

# # daisysms API variables
# API_KEY = 'VRAfaLkEvw3YHkgvXV09wafxBPT46e'
# COUNTRY_CODE = '55'  # country number from daisysms API country list
# MAX_PRICE = '0.6'

def create_driver_session():
    driver = webdriver.Remote('http://127.0.0.1:4727', options=options)
    return driver

# Generate random number between a minimum and maximum
def generate_random_number(min_value, max_value):
    return random.randint(min_value, max_value)

# Function to fetch the phone number from SMSpool API
def get_phone_number():
    url = 'https://api.smspool.net/purchase/sms'
    headers = {
        'Authorization': f'Bearer {API_KEY}'
    }
    data = {
        'key': API_KEY,
        'country': COUNTRY_CODE,
        'service': 'Bumble',  # Makes sure the code gets returned well!
        'max_price': MAX_PRICE,
        'quantity': '1',
        'pricing_option': '0'  # Cheapest option
    }

    response = requests.post(url, data=data, headers=headers)
    response_data = response.json()

    # Print the full response for debugging
    print("API Response:", response_data)
    
    # Extract phone number and order_id correctly
    if response_data.get('success') == 1:
        phone_number = response_data.get('phonenumber')  # Updated key
        order_id = response_data.get('orderid')  # Updated key for order ID
        print(f"Phone number purchased: {phone_number}")
        return phone_number, order_id
    else:
        print("Failed to get phone number:", response_data.get('message', 'No message'))
        return None, None

# Function to poll for the SMS verification code
def get_verification_code(order_id):
    url = 'https://api.smspool.net/sms/check'
    headers = {
        'Authorization': f'Bearer {API_KEY}'
    }
    data = {
        'key': API_KEY,
        'orderid': order_id
    }
    # Polling until the SMS is received
    for _ in range(10):  # Poll up to 10 times (adjust the number and delay as needed)
        response = requests.post(url, data=data, headers=headers)
        response_data = response.json()

        # Print the full response for debugging
        print("SMS Check Response:", response_data)
        # driver.get_log('client')

        # Fetch the SMS code from the correct key in the response
        if 'sms' in response_data:
            sms_code = response_data['sms']
            print(f"Received SMS code: {sms_code}")
            return sms_code
        else:
            print("No SMS yet. Retrying in 10 seconds...")
            time.sleep(10)  # Wait for 10 seconds before retrying

    print("Failed to receive SMS.")

    driver.background_app(2)
    
    didntgetcode = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Didn\'t get a code?"`]')
    didntgetcode.click()
    time.sleep(7.7)
    sendagain = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send again"`]')
    sendagain.click()
    time.sleep(7.7)

    # Polling until the SMS is received
    for _ in range(10):  # Poll up to 10 times (adjust the number and delay as needed)
        response = requests.post(url, data=data, headers=headers)
        response_data = response.json()

        # Print the full response for debugging
        print("SMS Check Response:", response_data)

        # Fetch the SMS code from the correct key in the response
        if 'sms' in response_data:
            sms_code = response_data['sms']
            print(f"Received SMS code: {sms_code}")
            return sms_code
        else:
            print("No SMS yet. Retrying in 10 seconds...")
            time.sleep(10)  # Wait for 10 seconds before retrying
    return None

# # Function to fetch the phone number from Daisy API
# def get_phone_number():
#     url = 'https://daisysms.com/stubs/handler_api.php'
#     data = {
#         'api_key': API_KEY,  # Replace with your actual API key
#         'action': 'getNumber',
#         'service': 'vz',  # 'vz' for bumble
#         'max_price': 0.40  # Adjust max price as needed
#     }

#     response = requests.get(url, params=data)
#     response_data = response.text.split(':')

#     # Print the full response for debugging
#     print("API Response:", response_data)

#     if response_data[0] == 'ACCESS_NUMBER':
#         order_id = response_data[1]
#         phone_number = response_data[2]

#         # Remove leading '1' from the phone number if it starts with '1'
#         if phone_number.startswith('1'):
#             phone_number = phone_number[1:]

#         print(f"Phone number purchased: {phone_number}")
#         return phone_number, order_id
#     else:
#         print("Failed to get phone number:", response_data[0])
#         return None, None

# # Function to poll for the SMS verification code
# def get_verification_code(order_id):
#     url = 'https://daisysms.com/stubs/handler_api.php'
#     data = {
#         'api_key': API_KEY,  # Replace with your actual API key
#         'action': 'getStatus',
#         'id': order_id
#     }

#     # Polling until the SMS is received
#     for _ in range(10):  # Poll up to 10 times (adjust the number and delay as needed)
#         response = requests.get(url, params=data)
#         response_data = response.text.split(':')

#         # Print the full response for debugging
#         print("SMS Check Response:", response_data)

#         if response_data[0] == 'STATUS_OK':
#             sms_code = response_data[1]
#             print(f"Received SMS code: {sms_code}")
#             return sms_code
#         elif response_data[0] == 'STATUS_WAIT_CODE':
#             print("No SMS yet. Retrying in 10 seconds...")
#             time.sleep(10)  # Wait for 10 seconds before retrying
#         else:
#             print(f"Failed to get SMS code: {response_data[0]}")
#             break

#     print("Failed to receive SMS.")
#     # Optionally handle retries using Appium code here for resending the code
#     return None

def enter_sms_code(driver, sms_code):
    # Ensure the code is 6 digits long
    if len(sms_code) != 6:
        print(f"Invalid SMS code length: {sms_code}")
        return
    
    # # back to home
    # driver.execute_script('mobile:pressButton', {"name": "home"})

    # time.sleep(2)

    # # Open bumble app
    # el16 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "bumble"`]')
    # el16.click()

    # Enter each digit in the corresponding text field
    try:
        # First digit
        el1 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[1]/XCUIElementTypeTextField")
        el1.clear()
        el1.send_keys(sms_code[0])
        
        # Second digit
        el2 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeTextField")
        el2.clear()
        el2.send_keys(sms_code[1])
        
        # Third digit
        el3 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeTextField")
        el3.clear()
        el3.send_keys(sms_code[2])
        
        # Fourth digit
        el4 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeTextField")
        el4.clear()
        el4.send_keys(sms_code[3])
        
        # Fifth digit
        el5 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeTextField")
        el5.clear()
        el5.send_keys(sms_code[4])
        
        # Sixth digit
        el6 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[6]/XCUIElementTypeTextField")
        el6.clear()
        el6.send_keys(sms_code[5])

        print(f"SMS code {sms_code} entered successfully!")
        
    except Exception as e:
        print(f"Error during SMS code entry: {e}")


# Perform touch action using W3C actions
def perform_touch_action(driver, x, y, hold_duration=0.1):
    try:
        print(f"Performing touch action at ({x}, {y}) with hold duration of {hold_duration} seconds.")
        touch = PointerInput(POINTER_TOUCH, "touch")
        actions = ActionChains(driver)
        actions.w3c_actions.pointer_action.move_to_location(x, y)
        actions.w3c_actions.pointer_action.pointer_down()
        actions.w3c_actions.pointer_action.pause(hold_duration)
        actions.w3c_actions.pointer_action.pointer_up()
        actions.perform()
        print(f"Touch action completed at ({x}, {y}).")
    except Exception as e:
        print(f"Error during touch action: {e}")

# Tap and hold at a specific location
def tap_and_hold(driver, x, y, hold_duration=2):
    try:
        touch = PointerInput(POINTER_TOUCH, "touch")
        actions = ActionChains(driver)
        print(f"Tapping and holding at ({x}, {y}) for {hold_duration} seconds.")
        actions.w3c_actions.pointer_action.move_to_location(x, y)
        actions.w3c_actions.pointer_action.pointer_down()
        actions.w3c_actions.pointer_action.pause(hold_duration)
        actions.w3c_actions.pointer_action.pointer_up()
        actions.perform()
        print(f"Hold and release completed at ({x}, {y}).")
    except Exception as e:
        print(f"Error during tap and hold: {e}")

def random_date(min_date_str, max_date_str):
    # Convert strings to datetime objects
    min_date = datetime.strptime(min_date_str, "%Y-%m-%d")
    max_date = datetime.strptime(max_date_str, "%Y-%m-%d")

    # Generate a random number of days between the two dates
    delta = max_date - min_date
    random_days = random.randint(0, delta.days)

    # Add the random number of days to the minimum date and return it formatted as DD MM YYYY
    return (min_date + timedelta(days=random_days)).strftime("%m %d %Y")

def extract_verification_code_from_subject_and_body(subject, body):
    # Example function to extract the verification code (assuming it's a 6-digit code)
    import re
    # Try to extract from subject first
    match = re.search(r'\d{6}', subject)
    if match:
        return match.group(0)
    
    # If not found in subject, try the body
    match = re.search(r'\d{6}', body)
    if match:
        return match.group(0)
    
    return None

def get_verification_code_from_email(emailAddress, emailPassword, max_attempts=1, poll_delay=10):
    for attempt in range(max_attempts):  # Poll up to max_attempts times
        try:
            print(f"Connecting to IMAP server for {emailAddress}...")
            addy = imaplib.IMAP4_SSL("imap.gmx.com", 993)  # for GMX 

            print(f"Logging in as {emailAddress}...")
            addy.login(emailAddress, emailPassword)
            print(f"Successfully logged in as {emailAddress}.")

            print(f"Selecting the inbox for {emailAddress}...")
            addy.select("inbox")

            print(f"Searching for emails with 'Verification' in the subject for {emailAddress}...")
            result, data = addy.search(None, '(SUBJECT "Verification")')

            if result != 'OK':
                print(f"Search failed with result: {result}")
                return None

            email_ids = data[0].split()

            if not email_ids:
                print(f"No verification emails found for {emailAddress}. Retrying in {poll_delay} seconds...")
                time.sleep(poll_delay)
                continue

            print(f"Found {len(email_ids)} email(s) for {emailAddress}. Fetching the latest one...")

            latest_email_id = email_ids[-1]
            result, msg_data = addy.fetch(latest_email_id, "(RFC822)")

            if result != 'OK':
                print(f"Failed to fetch email with result: {result}")
                return None

            for response_part in msg_data:
                if isinstance(response_part, tuple):
                    msg = email.message_from_bytes(response_part[1])

                    email_subject = decode_header(msg["Subject"])[0][0]
                    if isinstance(email_subject, bytes):
                        email_subject = email_subject.decode()

                    print(f"Email subject: {email_subject}")

                    if msg.is_multipart():
                        for part in msg.walk():
                            if part.get_content_type() == "text/plain":
                                email_body = part.get_payload(decode=True).decode('utf-8', errors='replace')
                                print(f"Email body (text/plain): {email_body}")
                                break
                    else:
                        email_body = msg.get_payload(decode=True).decode('utf-8', errors='replace')
                        print(f"Email body: {email_body}")

                    verification_code = extract_verification_code_from_subject_and_body(email_subject, email_body)
                    if verification_code:
                        print(f"Verification code found for {emailAddress}: {verification_code}")
                        return verification_code
                    else:
                        print(f"No verification code found in the email for {emailAddress}. Retrying in {poll_delay} seconds...")
                        time.sleep(poll_delay)
                        continue

        except Exception as e:
            print(f"Failed to retrieve email for {emailAddress}. Error: {e}")
            time.sleep(poll_delay)
            continue

        finally:
            print(f"Logging out of email for {emailAddress}.")
            addy.logout()

    print(f"Failed to receive verification code after {max_attempts} attempts.")
    return None

def enter_email_verification_code(driver, email_verification_code):
    try:
        # Ensure the email verification code is exactly 6 digits long
        if len(email_verification_code) != 6:
            print("Invalid email verification code length. Must be 6 digits.")
            return

        time.sleep(0.7)

        # Define both potential element path templates
        alternative_element_paths = [
            # First digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[1]/XCUIElementTypeTextField",
            # Second digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[2]/XCUIElementTypeTextField",
            # Third digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[3]/XCUIElementTypeTextField",
            # Fourth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[4]/XCUIElementTypeTextField",
            # Fifth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[5]/XCUIElementTypeTextField",
            # Sixth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[6]/XCUIElementTypeTextField"
        ]

        primary_element_paths = [
            # First digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[1]/XCUIElementTypeTextField",
            # Second digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[2]/XCUIElementTypeTextField",
            # Third digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[3]/XCUIElementTypeTextField",
            # Fourth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[4]/XCUIElementTypeTextField",
            # Fifth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[5]/XCUIElementTypeTextField",
            # Sixth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[6]/XCUIElementTypeTextField"
        ]

        # Function to attempt entering the code using given element paths
        def enter_code(paths):
            for index, path in enumerate(paths):
                el = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=path)
                el.clear()
                el.send_keys(email_verification_code[index])
                print(f"Entered digit {email_verification_code[index]} in field {index + 1}")

        # First attempt with primary paths
        try:
            enter_code(primary_element_paths)
            print("Clicking the Next button...")
            next_button = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
            next_button.click()
            print("Email verification code entered successfully using primary paths!")
        except Exception as primary_error:
            print(f"Primary paths failed: {primary_error}")

        # Try alternative paths if primary paths failed
        try:
            enter_code(alternative_element_paths)
            print("Clicking the Next button...")
            next_button = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
            next_button.click()
            print("Email verification code entered successfully using alternative paths!")
        except Exception as alternative_error:
            print(f"Both primary and alternative paths failed: {alternative_error}")
    
    except Exception as e:
        print(f"Error entering email verification code: {e}")

def retrieve_and_enter_verification_code(driver, email_address, email_password, max_attempts=3, delay_between_attempts=10):
    attempts = 0
    code_resent = False  # Track if the code has been resent

    while attempts < max_attempts:
        # Check if the session is still active
        if driver.session_id is None:
            print("Session terminated. Reinitializing the driver session...")
            driver = create_driver_session()  # Replace with your session initialization code
            driver.activate_app("com.moxco.bumble.ios")  # Reopen the app

        # Try to get the verification code from the email
        print(f"Attempt {attempts + 1}/{max_attempts}: Checking for verification code...")
        verification_code = get_verification_code_from_email(email_address, email_password, max_attempts=1)

        if verification_code:
            print(f"Final Verification Code for {email_address}: {verification_code}")
            enter_email_verification_code(driver, verification_code)

            # Click the 'Next' button to proceed
            try:
                print("Clicking the Next button...")
                next_button = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                next_button.click()

                # Wait for 4 seconds after clicking 'Next' for the Date Input Field to appear
                print("Waiting for 15 seconds to check for the Date Input Field...")
                time.sleep(15)

                # Try to find the 'Date Input Field'
                date_input_field = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Date Input Field"`]')
                print("Date Input Field found. Proceeding...")
                return True  # Exit the function after successful verification and transition

            except Exception as e:
                print(f"Date Input Field not found or couldn't click Next. Restarting the app. Error: {e}")

                # Restart the app and session
                try:
                    driver.terminate_app('com.moxco.bumble.ios')
                    driver.activate_app('com.moxco.bumble.ios')
                    time.sleep(15)
                except Exception as e:
                    print(f"Error during app restart: {e}")
                    driver.quit()  # Terminate the session
                    driver = create_driver_session()  # Reinitialize the session
                    driver.activate_app("com.moxco.bumble.ios")  # Reopen the app

                print("App reopened. Re-entering email and verification code...")

                try:
                    # filling in the email account
                    el23 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "<EMAIL>"`]')
                    el23.send_keys(email_address)

                    time.sleep(2.7)

                    el24 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                    el24.click()

                    time.sleep(7.7)

                    #### LOGIN WITH OTHER EMAIL WORKFLOW

                    # Clicking "No Thanks" instead of logging in somewhere
                    el25 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "No thanks"`]')
                    el25.click()

                except Exception:
                    pass

                time.sleep(7.7)

                # el24 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                # el24.click()

                # time.sleep(7.7)
                print("Re-entering the verification code...")
                enter_email_verification_code(driver, verification_code)  # Re-enter the code

        else:
            print(f"Failed to retrieve verification code for {email_address}, attempt {attempts + 1}/{max_attempts}")

        # Wait before attempting again
        time.sleep(delay_between_attempts)
        attempts += 1

        # If attempts exceed max_attempts and code hasn't been resent, trigger the resend code logic
        if attempts >= max_attempts and not code_resent:
            print("Attempting to resend verification code after the maximum failed attempts.")
            try:
                trouble_verifying = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Having trouble verifying?"`]')
                trouble_verifying.click()
                print("Clicked on 'Having trouble verifying?'")

                time.sleep(7.7)

                resend_code = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send code again"`]')
                resend_code.click()
                print("Clicked on 'Send code again'")

                print("Verification code resent.")
                code_resent = True  # Mark that the code has been resent

                # Reset attempts to restart the process after resending the code
                attempts = 0
                print("Restarting verification code checking process after resending code.")

            except Exception as e:
                print(f"Couldn't resend the code: {e}")
                return False

    print(f"Verification code not retrieved after {max_attempts} attempts.")
    return False

def click_next_and_reopen_if_needed(driver, verification_code, emailAddress):
    # Try clicking "Next" button
    try:
        el39 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el39.click()
        time.sleep(7.7)  # Wait to see if the next screen loads

        # Check if the expected element on the next screen is present
        next_screen_element = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Date Input Field"`]')
        if next_screen_element:
            print("Next screen loaded successfully.")
            return True

    except Exception as e:
        print("Failed to find or click 'Next' button or load the next screen:", e)

    # If we reach here, it means "Next" didn't work, so fully close and reopen the app
    print("Closing and reopening the app due to failure to advance...")

    # Fully close and relaunch the app
    driver.close_app()
    time.sleep(2)  # Small delay to ensure the app fully closes
    driver.launch_app()
    time.sleep(7)  # Wait for app to relaunch fully

    # Resend the code without fetching again (using the already fetched `verification_code`)
    try:
        print(f"Resending verification code: {verification_code}")
        emailfield = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "<EMAIL>"`]')
        emailfield.send_keys(emailAddress)
        time.sleep(0.77)
        emailnext = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        emailnext.click()
        time.sleep(2.8)
        enter_email_verification_code(driver, verification_code)
        time.sleep(2)  # Give time for verification code to be processed
    except Exception as e:
        print("Failed to re-enter the verification code after reopening the app:", e)

    return False

def tap_random_times(driver, x, y, hold_duration=0.2):
    # Randomly choose between 1, 2, or 3 taps
    tap_count = random.choice([1, 2, 3])
    print(f"Tapping and holding {tap_count} time(s).")
    
    # Perform the tap and hold action the chosen number of times
    for _ in range(tap_count):
        tap_and_hold(driver, x, y, hold_duration)

def perform_randomized_image_selection(driver):
    # Define the tap actions (coordinates for selecting images) in a list
    image_selection_actions = [
        (72, 175, 0.2),
        (194, 182, 0.2),
        (335, 173, 0.2),
        (66, 304, 0.2),
        (208, 299, 0.2),
        (313, 306, 0.2)
    ]

    # Shuffle the actions to randomize the order
    random.shuffle(image_selection_actions)

    # Perform each tap action in the randomized order
    for x, y, hold_duration in image_selection_actions:
        tap_and_hold(driver, x, y, hold_duration)
        time.sleep(0.9)

def perform_randomized_image_selection4deletion(driver):
    # Define the tap actions (coordinates for selecting images) in a list
    image_selection_actions = [
        (61, 429, 0.2),
        (188, 436, 0.2),
        (328, 419, 0.2),
        (60, 530, 0.2),
        (189, 520, 0.2),
        (313, 538, 0.2)
    ]

    # Shuffle the actions to randomize the order
    random.shuffle(image_selection_actions)

    # Perform each tap action in the randomized order
    for x, y, hold_duration in image_selection_actions:
        tap_and_hold(driver, x, y, hold_duration)
        time.sleep(0.9)

def find_correct_sliders(driver):
    # Loop through possible values for both minimum and maximum between 18 and 38
    for min_value in range(18, 39):  # Values from 18 to 38
        for max_value in range(18, 39):
            try:
                # Try finding the minimum slider
                el_min = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeSlider[`value == "minimum: {min_value}"`]')
                print(f"Found the minimum slider with value: {min_value}")
                
                # Try finding the maximum slider
                el_max = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeSlider[`value == "maximum: {max_value}"`]')
                print(f"Found the maximum slider with value: {max_value}")

                # If both are found, return them
                return el_min, el_max

            except Exception as e:
                # If it doesn't find the slider, continue searching
                print(f"Combination {min_value} and {max_value} did not match. Trying next...")

    # If no matching sliders are found, raise an error
    raise Exception("No valid sliders found in the range 18 to 38.")

def scroll_down(driver):

    # Calculate swipe start and end points (middle of the screen horizontally)
    start_x = 234
    start_y = 446  
    end_y = 172    # End near the top

    # Perform the scroll using 'mobile: swipe' for iOS
    driver.execute_script('mobile: swipe', {
        'direction': 'up',
        'startX': start_x,
        'startY': start_y,
        'endX': start_x,
        'endY': end_y,
        'duration': 800  # Duration in milliseconds
    })

def scroll_and_find_element(driver, element_locator, max_scrolls=10):
    scroll_attempts = 0

    while scroll_attempts < max_scrolls:
        try:
            # Try to find the element using its locator
            element = driver.find_element(AppiumBy.IOS_CLASS_CHAIN, element_locator)
            
            # If found, click the element
            element.click()
            print("Element found and clicked.")
            return True

        except NoSuchElementException:
            # If the element is not found, scroll down
            print(f"Element not found. Scrolling down... Attempt {scroll_attempts + 1}/{max_scrolls}")
            scroll_down(driver)
            scroll_attempts += 1

    print("Element not found after maximum scroll attempts.")
    return False

# THE PROCESS
def main_process(driver, variables):
    try:
        # Extract variables from the dictionary
        containerName = variables["containerName"]
        GPSlocation = variables["GPSlocation"]
        VPNlocation = variables["VPNlocation"]
        countryshort = variables["countryshort"]
        girlName = variables["girlName"]
        emailAddress = variables["emailAddress"]
        emailPassword = variables["emailPassword"]
        minimumDate = variables["minimumDate"]
        maximumDate = variables["maximumDate"]
        hometown = variables["hometown"]
        workplace = variables["workplace"]
        jobtitle = variables["jobtitle"]    
        college = variables["college"]
        promptAnswer1 = variables["promptAnswer1"]
        promptAnswer2 = variables["promptAnswer2"]
        promptAnswer3 = variables["promptAnswer3"]
        randomstring = ''.join(random.choices(string.ascii_letters + string.digits, k=8))

        driver = webdriver.Remote('http://127.0.0.1:4727', options=options)

        driver.execute_script('mobile:pressButton', {"name": "home"})

        # time.sleep(4)

        # # Tap and hold on the bumble app (put standardly at right bottom pinned) and clean Appdata
        # tap_and_hold(driver, 319, 623, hold_duration=2)
        # time.sleep(0.7)
        # print('Clicking AppData')
        # driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "AppData"`]').click()        
        # time.sleep(3)
        # print('Clicking Clear Caches')
        # tap_and_hold(driver, 110, 384, hold_duration=2) 
        # # driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Clear Caches"`]').click() 
        # time.sleep(0.7)
        # # print('Clicking Clear App Data')
        # # driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Clear App Data"`]').click() 
        # # time.sleep(1.7)
        # # driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Clear"`]').click()
        # # time.sleep(1.9)
        # tap_and_hold(driver, 263, 380, 0.2)    
        # time.sleep(1)
        # driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Reset"`]').click()
        # time.sleep(1)
        # tap_and_hold(driver, 277, 279, hold_duration=0.2)
        # time.sleep(3)
        # print('App Data Cleared.')

        # driver.execute_script('mobile:pressButton', {"name": "home"})
        # time.sleep(1)
        # driver.execute_script('mobile:pressButton', {"name": "home"})
        # time.sleep(2)

        # # Go home to be sure, open GPS Master
        # driver.execute_script('mobile:pressButton', {"name": "home"})
        # time.sleep(2)
        # el4 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "GPS Master"`]')
        # el4.click()

        # # set location
        # time.sleep(3)
        # el5 = driver.find_element(by=AppiumBy.ACCESSIBILITY_ID, value="Fake GPS")
        # el5.click()
        # time.sleep(3.7)
        # el6 = driver.find_element(by=AppiumBy.ACCESSIBILITY_ID, value="Search")
        # el6.click()
        # time.sleep(3.7)
        # el7 = driver.find_element(by=AppiumBy.ACCESSIBILITY_ID, value="Search Maps")
        # el7.click()
        # time.sleep(3.7)
        # el10 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeSearchField[`name == "Search Maps"`]')
        # el10.clear()
        # el10.send_keys(GPSlocation)
        # time.sleep(5.7)

        # el9 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeTable/XCUIElementTypeCell/XCUIElementTypeOther[2]/XCUIElementTypeOther')
        # el9.click()

        # time.sleep(1.7)

        # # back to home
        # driver.execute_script('mobile:pressButton', {"name": "home"})
        # time.sleep(1)
        # driver.execute_script('mobile:pressButton', {"name": "home"})

        # # open Mullvad to rotate IP
        # el12 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Mullvad VPN"`]')
        # el12.click()

        # time.sleep(2)

        # # Changing IP location
        # try:
        #     el13 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "selectLocationButton"`]')
        #     el13.click()

        #     time.sleep(3)

        #     print("Filling in the Mullvad location.")
            
        #     # Send location to text field
        #     tap_and_hold(driver, 96, 125, hold_duration=0.2)
        #     el14 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Entry"`]')
        #     el14.clear()
        #     el14.send_keys(VPNlocation)  # Location Name
        #     time.sleep(3)

        #     # Select location
        #     el15 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeCell[`name == "cityLocationCell"`]/XCUIElementTypeOther[1]')
        #     el15.click()

        #     time.sleep(3)

        # except Exception as e:
        #     print(f"Error during interaction: {e}")
            
        # time.sleep(3)

        # # back to home
        # driver.execute_script('mobile:pressButton', {"name": "home"})

        # # back to home
        # driver.execute_script('mobile:pressButton', {"name": "home"})

        # time.sleep(4)
        # ## IN CASE OF IMAGE SPOOFING
        # # Open Photos and delete latest 6 images
        # el97 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Photos"`]')
        # el97.click()

        # driver.terminate_app('com.apple.mobileslideshow')
        # driver.activate_app('com.apple.mobileslideshow')

        # time.sleep(3.78)
        # try:
        #     el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Albums"`]')
        #     el98.click()
        #     time.sleep(3.78)
        # except Exception as e:
        #     pass

        # try:
        #     el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Recents"`]')
        #     el98.click()
        # except Exception as e:
        #     pass

        # time.sleep(3.78)
        # try:
        #     el99 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Select"`]')
        #     el99.click()
        #     if not el99:
        #         print("gay shit wuth pic deletion")
        #         return
        # except Exception as e:
        #     el99 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Select"`]')
        #     el99.click()
        #     if not el99:
        #         print("gay shit wuth pic deletion")
        #         return

        # time.sleep(3.78)
        # perform_randomized_image_selection4deletion(driver)
        # time.sleep(3.7)
        # el100 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Delete"`]')
        # el100.click()
        # time.sleep(3.78)
        # el101 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Delete 6 Photos"`]')
        # el101.click()
        # time.sleep(3.77)
        # driver.execute_script('mobile:pressButton', {"name": "home"})
        # driver.execute_script('mobile:pressButton', {"name": "home"})

        # time.sleep(3.77)

        # # Tap and hold on the bumble app (put standardly at right bottom pinned) and create new crane container
        # tap_and_hold(driver, 319, 623, hold_duration=2)
        # time.sleep(0.07)
        # tap_and_hold(driver, 280, 550, hold_duration=0.2)
        # time.sleep(0.07)
        # scroll_and_find_element(driver, '**/XCUIElementTypeButton[`name == "New Container"`]', 10)
        # tap_and_hold(driver, 280, 491, hold_duration=0.2)
        # time.sleep(0.07)

        # # Checking for name field, filling in new container name and creating the container
        # try:
        #     print("Filling in the container name.")
            
        #     # Send input to a text field
        #     el2 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeTextField")
        #     el2.clear()
        #     el2.send_keys(containerName)  #Container Name
            
        #     # Click "Next" button
        #     el3 = driver.find_element(by=AppiumBy.ACCESSIBILITY_ID, value="Create")
        #     el3.click()

        #     print("Finalized container creation.")

        # except Exception as e:
        #     print(f"Error during interaction: {e}")

        # driver.execute_script('mobile:pressButton', {"name": "home"})
        # driver.execute_script('mobile:pressButton', {"name": "home"})

# Open bumble app
        el16 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Bumble"`]')
        el16.click()

        time.sleep(12)

        # Step 2: Tap "Create account" button
        el17 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "bumble.button.sign_in.phone.text"`]')
        el17.click()

        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`name == "registration.input.country.code.textfield"`]').click()

        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeCell[`name == "registration.countrypicker.cell"`][2]').click()

        # click UK for UK number
        tap_and_hold(driver, 181, 153, 0.2)

        try:
            # Step 1: Fetch phone number and order ID from sms API
            phone_number, orderid = get_phone_number()
            if phone_number is None or orderid is None:
                print("Could not fetch phone number. Exiting process.")
                return

            time.sleep(5.7)

            # Step 3: Fill the phone number fetched from the API
            el18 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[1]/XCUIElementTypeOther/XCUIElementTypeOther[1]/XCUIElementTypeTextField")
            el18.clear()
            el18.send_keys(phone_number)
            print(f"Phone number {phone_number} entered successfully!")
            
            try:
                el19 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                el19.click()
                print("Next button clicked successfully!")
            except Exception as e:
                print(f"Failed to click the Next button. Error: {e}")
                print("Terminating the process due to too many verification requests.")
                return  # Terminate the process

        except Exception as e:
            print(f"Error during process: {e}")

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`name == "registration.input.phone.number.textfield"`]').send_keys(phone_number)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.forward.button"`]').click()
        time.sleep(2)
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "OK"`]').click()

        try:
            driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.explanation.cta"`]').click()
            time.sleep(6)
            driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.title.button"`]').click()
            time.sleep(3)
            driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Get a code instead"`]').click()
            time.sleep(5)

        except:
            print('No phone call - just an SMS! Good.')
            pass

        time.sleep(5.7)

        # Step 4: Poll for the SMS code from sms API
        sms_code = get_verification_code(orderid)
        if sms_code is None:
            print("Could not fetch SMS code. Sending again.")
            didntgetcode = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.title.button"`]')
            didntgetcode.click()
            time.sleep(7.7)
            sendagain = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeCell[`name == "options.retry"`]/XCUIElementTypeOther')
            sendagain.click()

        time.sleep(0.7)
        
        # Step 5: Enter the SMS code in 6 separate fields
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`name == "registration.input.code.0.textfield"`]').send_keys(sms_code)

        # next
        tap_and_hold(driver, 331, 409, 0.2)
        time.sleep(10)
        # location services
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.cta.main"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Allow Once"`]').click()
        time.sleep(2)

        # privacy screen
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Accept"`]').click()
        time.sleep(10)

        # lets make this about you
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.cta.main"`]').click()
        time.sleep(2)
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Ask App Not to Track"`]').click()
        time.sleep(2)
        
        # time.sleep(4)
        tap_and_hold(driver, 50, 232, 0.2)
        time.sleep(1)
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`name == "bumble.registration.name_and_dob.textfield.field"`]').send_keys(girlName)
        time.sleep(1)

        # mm - dd - yyyy
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "bumble.registration.name_and_dob.dob"`][1]/XCUIElementTypeOther').send_keys(random.randint(1, 12))
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "bumble.registration.name_and_dob.dob"`][2]/XCUIElementTypeOther').send_keys(random.randint(1, 29))
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "bumble.registration.name_and_dob.dob"`][3]/XCUIElementTypeOther').send_keys(random.randint(1985, 1998))

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.title.button"`]').click()
        time.sleep(3)


        tap_and_hold(driver, 343, 412, 0.2)
        time.sleep(3)

        tap_and_hold(driver, 264, 373, 0.2)
        time.sleep(2)

        tap_and_hold(driver, 246, 283, 0.2)
        time.sleep(2)

        tap_and_hold(driver, 338, 626, 0.2)
        time.sleep(2)

        tap_and_hold(driver, 336, 627, 0.2)
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.title.button"`]').click()
        time.sleep(2)

        tap_and_hold(driver, 336, 627, 0.2)
        time.sleep(2)

        tap_and_hold(driver, 280, 302, 0.2)
        time.sleep(2)

        tap_and_hold(driver, 336, 624, 0.2)
        time.sleep(2)

        relationchoice1 = random.choice(['**/XCUIElementTypeOther[`name == "bumble.onboarding.multi_select.option.Relationship"`]', '**/XCUIElementTypeOther[`name == "bumble.onboarding.multi_select.option.LifePartner"`]', '**/XCUIElementTypeOther[`name == "bumble.onboarding.multi_select.option.Casual"`]', '**/XCUIElementTypeOther[`name == "bumble.onboarding.multi_select.option.Intimacy"`]', '**/XCUIElementTypeOther[`name == "bumble.onboarding.multi_select.option.Marriage"`]'])
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=relationchoice1).click()

        time.sleep(2)

        relationchoice2 = random.choice(['**/XCUIElementTypeOther[`name == "bumble.onboarding.multi_select.option.Relationship"`]', '**/XCUIElementTypeOther[`name == "bumble.onboarding.multi_select.option.LifePartner"`]', '**/XCUIElementTypeOther[`name == "bumble.onboarding.multi_select.option.Casual"`]', '**/XCUIElementTypeOther[`name == "bumble.onboarding.multi_select.option.Intimacy"`]', '**/XCUIElementTypeOther[`name == "bumble.onboarding.multi_select.option.Marriage"`]'])
        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=relationchoice2).click()
        # time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.forward.button"`]').click()
        # time.sleep(2)

        tap_random_times(driver, 191, 390, 0.2)
        tap_random_times(driver, 183, 409, 0.2)
        # time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.forward.button"`]').click()

        time.sleep(7)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeButton[`name == "bff_interests_search.group.badge"`][{random.randint(1, 9)}]').click()
        time.sleep(2)

        scroll_down(driver)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeButton[`name == "bff_interests_search.group.badge"`][{random.randint(11, 24)}]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeButton[`name == "bff_interests_search.group.badge"`][{random.randint(11, 24)}]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeButton[`name == "bff_interests_search.group.badge"`][{random.randint(11, 24)}]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "bffonboarding.footer.continue.button"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeButton[`name == "LifestyleMultiSelectBadgeView.BadgeOption.{random.randint(0, 13)}_unselected"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.forward.button"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.title.button"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.title.button"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.title.button"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.title.button"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.title.button"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeCell[`name == "registration.uploadphotos.photos.collection.cell.0"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "photoupload.cameraRoll"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Select Photos…"`]').click()
        time.sleep(5)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "gallery.empty.actionButton"`]').click()
        time.sleep(2)

        tap_and_hold(driver, 84, 250, 0.2)

        tap_and_hold(driver, 199, 242, 0.2)

        tap_and_hold(driver, 331, 252, 0.2)

        tap_and_hold(driver, 69, 388, 0.2)
        
        time.sleep(5)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]').click()
        time.sleep(5)

        tap_and_hold(driver, 60, 360, 0.2)

        tap_and_hold(driver, 154, 634, 0.2)

        tap_and_hold(driver, 245, 624, 0.2)

        tap_and_hold(driver, 326, 624, 0.2)

        time.sleep(4)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "gallery.nav.rightItem"`]').click()
        time.sleep(5)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.forward.button"`]').click()
        time.sleep(8)

        try:
            driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "connect.instagram.secondary_button"`]').click()
            time.sleep(5)
        except:
            pass

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeButton[`name == "bumble.screener_question_selection.question_message"`][{random.randint(2,5)}]').click()
        time.sleep(4)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "registration.footer.forward.button"`]').click()
        time.sleep(3)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "bumble.opening_move_explanation.continue_button"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "com.bumble.pledge.accept"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "com.bumble.pledge.accept"`]').click()
        time.sleep(2)

        driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Allow While Using App"`]').click()

        try:
            driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "ctabox.primary.button"`]').click()
            print(f'Force verification on {containerName}')
        except Exception:
            pass

        driver.execute_script('mobile:pressButton', {"name": "home"})
        time.sleep(7.7)

    except Exception as e:
        print(f"Error during process for {containerName}: {e}")


# Loop through the iterations_data list and call the main_process with each set of variables
for i, variables in enumerate(iterations_data):
    print(f"Starting process for iteration {i + 1}")
    main_process(driver, variables)
    print(f"Completed process for iteration {i + 1}")

# End the session
# driver.quit()
