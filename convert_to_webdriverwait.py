#!/usr/bin/env python3
"""
Script to help convert find_element calls to WebDriverWait patterns
"""

import re
import os

def analyze_find_element_patterns(file_path):
    """Analyze find_element patterns in a file"""
    print(f"\n🔍 Analyzing {file_path}...")
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    patterns = []
    
    for i, line in enumerate(lines, 1):
        # Look for find_element patterns
        if 'find_element' in line and 'driver.find_element' in line:
            # Check if there's a time.sleep before it
            has_sleep_before = False
            sleep_line = None
            if i > 1 and 'time.sleep' in lines[i-2]:
                has_sleep_before = True
                sleep_line = lines[i-2].strip()
            
            # Check what happens after (click, send_keys, etc.)
            action_after = None
            if i < len(lines):
                next_line = lines[i].strip()
                if '.click()' in next_line:
                    action_after = 'click'
                elif '.send_keys(' in next_line:
                    action_after = 'send_keys'
                elif '.clear()' in next_line:
                    action_after = 'clear'
            
            patterns.append({
                'line_num': i,
                'line': line.strip(),
                'has_sleep_before': has_sleep_before,
                'sleep_line': sleep_line,
                'action_after': action_after,
                'priority': get_priority(line, action_after)
            })
    
    return patterns

def get_priority(line, action_after):
    """Determine priority for conversion based on element type and action"""
    high_priority_elements = [
        'Button', 'Next', 'Create', 'Click', 'Submit', 'Done', 'OK'
    ]
    
    medium_priority_elements = [
        'TextField', 'StaticText', 'Image'
    ]
    
    # High priority: Interactive elements that users click
    if action_after == 'click':
        for element in high_priority_elements:
            if element in line:
                return 'HIGH'
    
    # Medium priority: Input fields
    if action_after in ['send_keys', 'clear']:
        return 'MEDIUM'
    
    # Low priority: Everything else
    return 'LOW'

def generate_webdriverwait_replacement(pattern):
    """Generate WebDriverWait replacement for a pattern"""
    line = pattern['line']
    action = pattern['action_after']
    
    # Extract the locator from the original line
    locator_match = re.search(r'by=AppiumBy\.([^,]+),\s*value=([^)]+)', line)
    if not locator_match:
        return None
    
    locator_type = locator_match.group(1)
    locator_value = locator_match.group(2)
    
    # Extract variable name
    var_match = re.search(r'(\w+)\s*=\s*driver\.find_element', line)
    var_name = var_match.group(1) if var_match else 'element'
    
    # Generate replacement based on action
    if action == 'click':
        replacement = f"""        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for {var_name}...")
            {var_name} = wait.until(EC.element_to_be_clickable((AppiumBy.{locator_type}, {locator_value})))
            {var_name}.click()
            print("✅ Clicked {var_name}")
        except TimeoutException:
            print("❌ Timeout waiting for {var_name}")
            return
        except Exception as e:
            print(f"❌ Error clicking {var_name}: {{e}}")
            return"""
    
    elif action in ['send_keys', 'clear']:
        replacement = f"""        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for {var_name}...")
            {var_name} = wait.until(EC.presence_of_element_located((AppiumBy.{locator_type}, {locator_value})))
            # Add your send_keys/clear logic here
            print("✅ Found {var_name}")
        except TimeoutException:
            print("❌ Timeout waiting for {var_name}")
            return
        except Exception as e:
            print(f"❌ Error with {var_name}: {{e}}")
            return"""
    
    else:
        replacement = f"""        try:
            wait = WebDriverWait(driver, 15)
            {var_name} = wait.until(EC.presence_of_element_located((AppiumBy.{locator_type}, {locator_value})))
        except TimeoutException:
            print("❌ Timeout waiting for {var_name}")
            return"""
    
    return replacement

def main():
    print("🔧 WebDriverWait Conversion Analysis")
    print("=" * 50)
    
    files = ['iX_hinge946new.py', 'i7deux_hinge946.py']
    
    for file_path in files:
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            continue
            
        patterns = analyze_find_element_patterns(file_path)
        
        # Sort by priority
        high_priority = [p for p in patterns if p['priority'] == 'HIGH']
        medium_priority = [p for p in patterns if p['priority'] == 'MEDIUM']
        low_priority = [p for p in patterns if p['priority'] == 'LOW']
        
        print(f"\n📊 {file_path} Summary:")
        print(f"   🔴 HIGH Priority (clickable elements): {len(high_priority)}")
        print(f"   🟡 MEDIUM Priority (input fields): {len(medium_priority)}")
        print(f"   🟢 LOW Priority (other elements): {len(low_priority)}")
        print(f"   📝 Total find_element calls: {len(patterns)}")
        
        # Show top 10 high priority items
        print(f"\n🔴 Top 10 HIGH Priority Items for {file_path}:")
        for i, pattern in enumerate(high_priority[:10], 1):
            print(f"   {i}. Line {pattern['line_num']}: {pattern['line'][:80]}...")
            if pattern['has_sleep_before']:
                print(f"      ⏰ Has time.sleep before: {pattern['sleep_line']}")
    
    print("\n" + "=" * 50)
    print("🎯 RECOMMENDATIONS:")
    print("1. Start with HIGH priority items (clickable buttons)")
    print("2. Focus on main workflow sections first")
    print("3. Convert sections with time.sleep + find_element patterns")
    print("4. Test each section after conversion")
    print("5. Keep time.sleep only for coordinate clicks")

if __name__ == "__main__":
    main()
