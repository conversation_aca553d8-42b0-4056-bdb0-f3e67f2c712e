#!/usr/bin/env python3
"""
Test script to verify WebDriverWait implementation in both files
"""

import ast
import re

def check_webdriverwait_usage(file_path):
    """Check if WebDriverWait is properly implemented in the file"""
    print(f"\n🔍 Analyzing {file_path}...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Check for imports
    has_webdriverwait_import = 'from selenium.webdriver.support.ui import WebDriverWait' in content
    has_ec_import = 'from selenium.webdriver.support import expected_conditions as EC' in content
    has_timeout_import = 'TimeoutException' in content
    
    print(f"✅ WebDriverWait import: {has_webdriverwait_import}")
    print(f"✅ Expected Conditions import: {has_ec_import}")
    print(f"✅ TimeoutException import: {has_timeout_import}")
    
    # Check for custom function removal
    has_custom_wait_functions = 'def wait_and_find_element' in content or 'def wait_and_click_element' in content
    print(f"✅ Custom wait functions removed: {not has_custom_wait_functions}")
    
    # Count WebDriverWait usage
    webdriverwait_count = len(re.findall(r'WebDriverWait\(driver,\s*\d+\)', content))
    print(f"✅ WebDriverWait usage count: {webdriverwait_count}")
    
    # Count expected_conditions usage
    ec_usage_count = len(re.findall(r'EC\.(element_to_be_clickable|presence_of_element_located)', content))
    print(f"✅ Expected Conditions usage count: {ec_usage_count}")
    
    # Check for time.sleep usage (should be reduced, except for coordinate clicks)
    time_sleep_count = len(re.findall(r'time\.sleep\(', content))
    print(f"⚠️  time.sleep usage count: {time_sleep_count} (should be minimal, only for coordinate clicks)")
    
    # Check for proper exception handling
    timeout_exception_count = len(re.findall(r'except TimeoutException', content))
    print(f"✅ TimeoutException handling count: {timeout_exception_count}")
    
    # Check Geranium function specifically
    geranium_function_match = re.search(r'def set_geranium_location.*?(?=def|\Z)', content, re.DOTALL)
    if geranium_function_match:
        geranium_function = geranium_function_match.group(0)
        geranium_webdriverwait = len(re.findall(r'WebDriverWait\(driver,\s*\d+\)', geranium_function))
        geranium_ec_usage = len(re.findall(r'EC\.(element_to_be_clickable|presence_of_element_located)', geranium_function))
        print(f"✅ Geranium function WebDriverWait usage: {geranium_webdriverwait}")
        print(f"✅ Geranium function EC usage: {geranium_ec_usage}")
    
    return {
        'webdriverwait_import': has_webdriverwait_import,
        'ec_import': has_ec_import,
        'timeout_import': has_timeout_import,
        'custom_functions_removed': not has_custom_wait_functions,
        'webdriverwait_count': webdriverwait_count,
        'ec_usage_count': ec_usage_count,
        'time_sleep_count': time_sleep_count,
        'timeout_exception_count': timeout_exception_count
    }

def main():
    print("🧪 Testing WebDriverWait Implementation")
    print("=" * 50)
    
    files_to_check = [
        'iX_hinge946new.py',
        'i7deux_hinge946.py'
    ]
    
    results = {}
    
    for file_path in files_to_check:
        try:
            results[file_path] = check_webdriverwait_usage(file_path)
        except FileNotFoundError:
            print(f"❌ File not found: {file_path}")
        except Exception as e:
            print(f"❌ Error analyzing {file_path}: {e}")
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    for file_path, result in results.items():
        print(f"\n📁 {file_path}:")
        if result['webdriverwait_import'] and result['ec_import'] and result['custom_functions_removed']:
            print("✅ GOOD: Proper WebDriverWait implementation")
        else:
            print("❌ NEEDS WORK: Implementation issues detected")
        
        if result['webdriverwait_count'] > 0 and result['ec_usage_count'] > 0:
            print("✅ GOOD: WebDriverWait and Expected Conditions are being used")
        else:
            print("❌ NEEDS WORK: WebDriverWait/EC usage is low")
    
    print("\n🎯 RECOMMENDATIONS:")
    print("1. ✅ Custom wait functions removed")
    print("2. ✅ Standard WebDriverWait + Expected Conditions implemented")
    print("3. ✅ Proper exception handling with TimeoutException")
    print("4. ⚠️  time.sleep should only be used for coordinate clicks")
    print("5. ✅ Geranium function uses WebDriverWait for all UI interactions")

if __name__ == "__main__":
    main()
