#!/usr/bin/env python3
"""
Test script to verify the email API integration
"""

import requests
import time
import re
from bs4 import BeautifulSoup

# Email API variables
EMAIL_API_TOKEN = '45qYH9xArudux8Ten3Veg8wur4XOLaDA'
EMAIL_API_BASE_URL = 'https://api.anymessage.shop'
EMAIL_SITE = 'hinge.co'
EMAIL_DOMAINS = 'gmail'

def test_email_balance():
    """Test getting balance from email API"""
    try:
        print("🔍 Testing email API balance...")
        url = f"{EMAIL_API_BASE_URL}/user/balance"
        params = {'token': EMAIL_API_TOKEN}
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data}")
            if data.get('status') == 'success':
                balance = data.get('balance')
                print(f"✅ Email API balance: {balance}")
                return float(balance)
            else:
                print(f"❌ Email API balance error: {data}")
                return None
        else:
            print(f"❌ Email API balance request failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error getting email balance: {e}")
        return None

def test_order_email():
    """Test ordering a new email address"""
    try:
        print("🔍 Testing email ordering...")
        url = f"{EMAIL_API_BASE_URL}/email/order"
        params = {
            'token': EMAIL_API_TOKEN,
            'site': EMAIL_SITE,
            'domain': EMAIL_DOMAINS
        }
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data}")
            if data.get('status') == 'success':
                email_id = data.get('id')
                email_address = data.get('email')
                print(f"✅ Email ordered successfully: {email_address} (ID: {email_id})")
                return email_id, email_address
            else:
                error_value = data.get('value', 'unknown error')
                print(f"❌ Email order error: {error_value}")
                return None, None
        else:
            print(f"❌ Email order request failed: {response.status_code}")
            return None, None
    except Exception as e:
        print(f"❌ Error ordering email: {e}")
        return None, None

def test_get_message(email_id, max_attempts=5):
    """Test getting email message (limited attempts for testing)"""
    try:
        print(f"🔍 Testing message retrieval for ID: {email_id}")
        url = f"{EMAIL_API_BASE_URL}/email/getmessage"
        params = {
            'token': EMAIL_API_TOKEN,
            'id': email_id
        }
        
        for attempt in range(max_attempts):
            print(f"Checking for email message (attempt {attempt + 1}/{max_attempts})...")
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data}")
                if data.get('status') == 'success':
                    message = data.get('message')
                    print(f"✅ Email message received!")
                    return message
                elif data.get('value') == 'wait message':
                    print(f"⏳ Email not received yet, waiting 5 seconds...")
                    time.sleep(5)
                    continue
                else:
                    print(f"❌ Email message error: {data}")
                    return None
            else:
                print(f"❌ Email message request failed: {response.status_code}")
                return None
        
        print(f"⏳ Email message not received after {max_attempts} attempts (this is normal for testing)")
        return None
    except Exception as e:
        print(f"❌ Error getting email message: {e}")
        return None

def test_extract_code():
    """Test code extraction with sample HTML"""
    sample_html = """
    <html>
    <body>
        <p>Your Hinge verification code is: 123456</p>
        <p>Please enter this code to verify your account.</p>
    </body>
    </html>
    """
    
    try:
        print("🔍 Testing verification code extraction...")
        soup = BeautifulSoup(sample_html, 'html.parser')
        text_content = soup.get_text()
        
        patterns = [
            r'verification code[:\s]*(\d{6})',
            r'code[:\s]*(\d{6})',
            r'(\d{6})',
            r'confirm[:\s]*(\d{6})',
            r'verify[:\s]*(\d{6})'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            if matches:
                code = matches[0]
                if len(code) == 6 and code.isdigit():
                    print(f"✅ Extracted verification code: {code}")
                    return code
        
        print("❌ Could not extract verification code")
        return None
        
    except Exception as e:
        print(f"❌ Error extracting verification code: {e}")
        return None

def main():
    print("🧪 Testing Email API Integration")
    print("=" * 50)
    
    # Test 1: Check balance
    balance = test_email_balance()
    if balance is None:
        print("❌ Cannot proceed without valid balance")
        return
    
    print(f"\n💰 Current balance: {balance}")
    
    # Test 2: Order email
    email_id, email_address = test_order_email()
    if not email_id or not email_address:
        print("❌ Cannot proceed without valid email order")
        return
    
    print(f"\n📧 Ordered email: {email_address} (ID: {email_id})")
    
    # Test 3: Try to get message (will likely timeout, but tests the API)
    message = test_get_message(email_id, max_attempts=3)
    if message:
        print(f"\n📨 Received message (first 200 chars): {message[:200]}...")
    else:
        print(f"\n⏳ No message received yet (normal for testing)")
    
    # Test 4: Test code extraction
    test_extract_code()
    
    print("\n" + "=" * 50)
    print("✅ Email API integration tests completed!")
    print(f"📧 Test email address: {email_address}")
    print(f"🆔 Test email ID: {email_id}")
    print("\n🎯 Integration is ready for Hinge automation!")
    print("The script will:")
    print("1. Order a Gmail address from the API")
    print("2. Use it in Hinge registration")
    print("3. Wait for and retrieve the verification email")
    print("4. Extract the 6-digit code automatically")

if __name__ == "__main__":
    main()
