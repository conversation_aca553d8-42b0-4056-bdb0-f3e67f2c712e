from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
from appium.options.ios import XCUITestOptions
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.actions.interaction import POINTER_TOUCH
from selenium.webdriver.common.actions.pointer_input import PointerInput
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException, WebDriverException
from datetime import datetime, timedelta
from email.header import decode_header
import random
import imaplib
import email
import re
import os
import base64
import re
import requests
import time
import socket
import json
import csv
from urllib3.exceptions import MaxRetryError, NewConnectionError

# ============================================================================
# ACCOUNT CREATION LOGGING SYSTEM
# ============================================================================

class AccountCreationLogger:
    def __init__(self, log_dir="logs"):
        """Initialize the logging system"""
        self.log_dir = log_dir
        self.ensure_log_directory()

        # File paths
        self.summary_file = os.path.join(log_dir, "account_creation_summary.json")
        self.detailed_log = os.path.join(log_dir, "detailed_account_log.csv")
        self.daily_stats = os.path.join(log_dir, f"daily_stats_{datetime.now().strftime('%Y-%m-%d')}.json")

        # Initialize files if they don't exist
        self.initialize_log_files()

        # Current session tracking
        self.session_start_time = datetime.now()
        self.session_accounts = []
        self.session_costs = {"daisysms": 0.0, "anymessage": 0.0, "total": 0.0}

    def ensure_log_directory(self):
        """Create logs directory if it doesn't exist"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
            print(f"📁 Created logs directory: {self.log_dir}")

    def initialize_log_files(self):
        """Initialize log files with headers if they don't exist"""
        # Initialize summary file
        if not os.path.exists(self.summary_file):
            summary_data = {
                "total_accounts_created": 0,
                "total_cost_usd": 0.0,
                "total_daisysms_cost": 0.0,
                "total_anymessage_cost": 0.0,
                "first_account_date": None,
                "last_account_date": None,
                "success_rate": 0.0,
                "total_attempts": 0
            }
            with open(self.summary_file, 'w') as f:
                json.dump(summary_data, f, indent=2)

        # Initialize detailed log CSV
        if not os.path.exists(self.detailed_log):
            with open(self.detailed_log, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    "timestamp", "container_name", "location", "girl_name",
                    "email_address", "phone_number", "success", "failure_reason",
                    "daisysms_cost", "anymessage_cost", "total_cost", "duration_minutes"
                ])

    def start_account_creation(self, container_name, location, girl_name):
        """Start tracking a new account creation attempt"""
        account_data = {
            "container_name": container_name,
            "location": location,
            "girl_name": girl_name,
            "start_time": datetime.now(),
            "email_address": None,
            "phone_number": None,
            "success": False,
            "failure_reason": None,
            "daisysms_cost": 0.0,
            "anymessage_cost": 0.0,
            "total_cost": 0.0,
            "duration_minutes": 0.0
        }
        self.session_accounts.append(account_data)
        return len(self.session_accounts) - 1  # Return index for tracking

    def log_phone_cost(self, account_index, phone_number, cost):
        """Log DaisySMS phone number cost"""
        if account_index < len(self.session_accounts):
            self.session_accounts[account_index]["phone_number"] = phone_number
            self.session_accounts[account_index]["daisysms_cost"] = cost
            self.session_accounts[account_index]["total_cost"] += cost
            self.session_costs["daisysms"] += cost
            self.session_costs["total"] += cost
            print(f"💰 DaisySMS cost logged: ${cost:.4f} for {phone_number}")

    def log_email_cost(self, account_index, email_address, cost):
        """Log anymessage email cost"""
        if account_index < len(self.session_accounts):
            self.session_accounts[account_index]["email_address"] = email_address
            self.session_accounts[account_index]["anymessage_cost"] = cost
            self.session_accounts[account_index]["total_cost"] += cost
            self.session_costs["anymessage"] += cost
            self.session_costs["total"] += cost
            print(f"💰 Anymessage cost logged: ${cost:.4f} for {email_address}")

    def log_account_success(self, account_index):
        """Mark account creation as successful (reached 'Start sending likes')"""
        if account_index < len(self.session_accounts):
            account = self.session_accounts[account_index]
            account["success"] = True
            account["duration_minutes"] = (datetime.now() - account["start_time"]).total_seconds() / 60

            print(f"🎉 ACCOUNT CREATION SUCCESS!")
            print(f"   Container: {account['container_name']}")
            print(f"   Duration: {account['duration_minutes']:.1f} minutes")
            print(f"   Total Cost: ${account['total_cost']:.4f}")

            # Save to files immediately
            self._save_account_to_files(account)
            self._update_summary_stats()

    def log_account_failure(self, account_index, failure_reason):
        """Mark account creation as failed"""
        if account_index < len(self.session_accounts):
            account = self.session_accounts[account_index]
            account["success"] = False
            account["failure_reason"] = failure_reason
            account["duration_minutes"] = (datetime.now() - account["start_time"]).total_seconds() / 60

            print(f"❌ ACCOUNT CREATION FAILED!")
            print(f"   Container: {account['container_name']}")
            print(f"   Reason: {failure_reason}")
            print(f"   Duration: {account['duration_minutes']:.1f} minutes")
            print(f"   Lost Cost: ${account['total_cost']:.4f}")

            # Save to files immediately
            self._save_account_to_files(account)
            self._update_summary_stats()

    def _save_account_to_files(self, account):
        """Save individual account data to CSV log"""
        with open(self.detailed_log, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([
                account["start_time"].strftime("%Y-%m-%d %H:%M:%S"),
                account["container_name"],
                account["location"],
                account["girl_name"],
                account["email_address"] or "N/A",
                account["phone_number"] or "N/A",
                account["success"],
                account["failure_reason"] or "N/A",
                f"{account['daisysms_cost']:.4f}",
                f"{account['anymessage_cost']:.4f}",
                f"{account['total_cost']:.4f}",
                f"{account['duration_minutes']:.1f}"
            ])

    def _update_summary_stats(self):
        """Update the summary statistics file"""
        # Load current summary
        with open(self.summary_file, 'r') as f:
            summary = json.load(f)

        # Calculate new stats
        successful_accounts = [acc for acc in self.session_accounts if acc["success"]]
        total_attempts = len(self.session_accounts)

        # Update summary
        summary["total_accounts_created"] += len(successful_accounts)
        summary["total_attempts"] += total_attempts
        summary["total_daisysms_cost"] += self.session_costs["daisysms"]
        summary["total_anymessage_cost"] += self.session_costs["anymessage"]
        summary["total_cost_usd"] += self.session_costs["total"]

        if successful_accounts:
            if not summary["first_account_date"]:
                summary["first_account_date"] = successful_accounts[0]["start_time"].strftime("%Y-%m-%d %H:%M:%S")
            summary["last_account_date"] = successful_accounts[-1]["start_time"].strftime("%Y-%m-%d %H:%M:%S")

        if summary["total_attempts"] > 0:
            summary["success_rate"] = (summary["total_accounts_created"] / summary["total_attempts"]) * 100

        # Save updated summary
        with open(self.summary_file, 'w') as f:
            json.dump(summary, f, indent=2)

    def print_session_summary(self):
        """Print summary of current session"""
        successful = [acc for acc in self.session_accounts if acc["success"]]
        failed = [acc for acc in self.session_accounts if not acc["success"]]

        print("\n" + "="*60)
        print("📊 SESSION SUMMARY")
        print("="*60)
        print(f"🎯 Accounts Created: {len(successful)}")
        print(f"❌ Failed Attempts: {len(failed)}")
        print(f"📈 Success Rate: {(len(successful)/len(self.session_accounts)*100):.1f}%" if self.session_accounts else "0%")
        print(f"💰 Total Session Cost: ${self.session_costs['total']:.4f}")
        print(f"   - DaisySMS: ${self.session_costs['daisysms']:.4f}")
        print(f"   - Anymessage: ${self.session_costs['anymessage']:.4f}")
        print(f"⏱️  Session Duration: {(datetime.now() - self.session_start_time).total_seconds()/60:.1f} minutes")

        if successful:
            avg_cost = sum(acc["total_cost"] for acc in successful) / len(successful)
            avg_duration = sum(acc["duration_minutes"] for acc in successful) / len(successful)
            print(f"📊 Average Cost per Success: ${avg_cost:.4f}")
            print(f"📊 Average Duration per Success: {avg_duration:.1f} minutes")

        print("="*60)

# Initialize global logger
account_logger = AccountCreationLogger()

# ============================================================================
# BAD PHONE NUMBER LOGGING SYSTEM
# ============================================================================

class BadPhoneNumberLogger:
    def __init__(self, log_dir="logs"):
        """Initialize the bad phone number logging system"""
        self.log_dir = log_dir
        self.bad_numbers_file = os.path.join(log_dir, "bad_phone_numbers.csv")
        self.bad_numbers_summary = os.path.join(log_dir, "bad_numbers_summary.json")

        # Ensure log directory exists
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        # Initialize files if they don't exist
        self.initialize_bad_numbers_log()

    def initialize_bad_numbers_log(self):
        """Initialize bad phone numbers log files"""
        # Initialize CSV file
        if not os.path.exists(self.bad_numbers_file):
            with open(self.bad_numbers_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    "timestamp", "phone_number", "order_id", "reason",
                    "cost_lost", "container_name", "location", "girl_name"
                ])

        # Initialize summary file
        if not os.path.exists(self.bad_numbers_summary):
            summary_data = {
                "total_bad_numbers": 0,
                "total_cost_lost": 0.0,
                "bad_numbers_list": [],
                "most_recent_bad_number": None,
                "last_updated": None
            }
            with open(self.bad_numbers_summary, 'w') as f:
                json.dump(summary_data, f, indent=2)

    def log_bad_phone_number(self, phone_number, order_id, reason, cost_lost, container_name, location, girl_name):
        """Log a bad phone number that caused issues"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Log to CSV
        with open(self.bad_numbers_file, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([
                timestamp, phone_number, order_id, reason,
                f"{cost_lost:.4f}", container_name, location, girl_name
            ])

        # Update summary
        with open(self.bad_numbers_summary, 'r') as f:
            summary = json.load(f)

        summary["total_bad_numbers"] += 1
        summary["total_cost_lost"] += cost_lost
        summary["bad_numbers_list"].append(phone_number)
        summary["most_recent_bad_number"] = phone_number
        summary["last_updated"] = timestamp

        with open(self.bad_numbers_summary, 'w') as f:
            json.dump(summary, f, indent=2)

        print(f"📱❌ BAD PHONE NUMBER LOGGED!")
        print(f"   Number: {phone_number}")
        print(f"   Reason: {reason}")
        print(f"   Cost Lost: ${cost_lost:.4f}")
        print(f"   Container: {container_name}")

    def get_bad_numbers_list(self):
        """Get list of all bad phone numbers to avoid"""
        try:
            with open(self.bad_numbers_summary, 'r') as f:
                summary = json.load(f)
                return summary.get("bad_numbers_list", [])
        except:
            return []

    def print_bad_numbers_summary(self):
        """Print summary of bad phone numbers"""
        try:
            with open(self.bad_numbers_summary, 'r') as f:
                summary = json.load(f)

            print("\n" + "="*60)
            print("📱❌ BAD PHONE NUMBERS SUMMARY")
            print("="*60)
            print(f"🚫 Total Bad Numbers: {summary['total_bad_numbers']}")
            print(f"💸 Total Cost Lost: ${summary['total_cost_lost']:.4f}")
            if summary['most_recent_bad_number']:
                print(f"🕐 Most Recent Bad Number: {summary['most_recent_bad_number']}")
                print(f"📅 Last Updated: {summary['last_updated']}")
            print("="*60)
        except:
            print("No bad phone numbers logged yet.")

# Initialize bad phone number logger
bad_phone_logger = BadPhoneNumberLogger()

def generate_container_name(test_number, location):
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return f"Test {test_number} ({location}) - {current_time}"

def robust_element_interaction(driver, action_func, max_retries=3, retry_delay=2):
    """
    Robust wrapper for any driver interaction that might fail due to socket hang ups.

    Args:
        driver: WebDriver instance
        action_func: Function that performs the driver action
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds

    Returns:
        Result of action_func or None if all retries failed
    """
    for attempt in range(max_retries):
        try:
            return action_func()
        except (WebDriverException, socket.error, MaxRetryError, NewConnectionError, ConnectionError) as e:
            error_msg = str(e)
            if any(keyword in error_msg.lower() for keyword in ['socket hang up', 'connection', 'proxy', 'remote server']):
                print(f"🔄 Network error on attempt {attempt + 1}/{max_retries}: {error_msg[:100]}...")
                if attempt < max_retries - 1:
                    print(f"⏳ Waiting {retry_delay} seconds before retry...")
                    time.sleep(retry_delay)
                    continue
                else:
                    print(f"❌ All {max_retries} attempts failed due to network issues")
                    return None
            else:
                # Re-raise non-network errors immediately
                raise e
        except Exception as e:
            # Re-raise other exceptions immediately
            raise e

    return None

def robust_find_element(driver, by, value, timeout=10, max_retries=3):
    """
    Robust element finding with socket hang up protection.
    """
    def find_action():
        wait = WebDriverWait(driver, timeout)
        return wait.until(EC.element_to_be_clickable((by, value)))

    return robust_element_interaction(driver, find_action, max_retries)

def robust_click_element(driver, element, max_retries=3):
    """
    Robust element clicking with socket hang up protection.
    """
    def click_action():
        element.click()
        return True

    return robust_element_interaction(driver, click_action, max_retries)

def robust_send_keys(driver, element, text, max_retries=3):
    """
    Robust text input with socket hang up protection.
    """
    def send_keys_action():
        element.clear()
        element.send_keys(text)
        return True

    return robust_element_interaction(driver, send_keys_action, max_retries)

# Function to parse emails in the format email:password
def parse_emails(email_string, separator=":"):
    emails = []
    lines = email_string.strip().split("\n")
    for line in lines:
        email, password = line.split(separator)
        emails.append({"emailAddress": email, "emailPassword": password})
    return emails

# Define girl name categories and corresponding Instagram handles
girl_names_and_handles = {
    "Irina": {
        "girlNames": ["Nina", "Ina", "Irina", "Rina", "Riri", "Ri", "Iva", "Ivana", "Inabella"],
        "ig_handle": "heyyitsirina"
    },
    "Sophie": {
        "girlNames": ["Sophie", "Sophia", "Phia", "Phi", "So", "Sof", "Soph", "Phie", "Sooph"],
        "ig_handle": "suedeesophiee"
    },
    "Hellena": {
        "girlNames": ["Hellen", "Lena", "Helen", "Helena", "Hella", "Leena", "Hel", "Helli"],
        "ig_handle": "hellohellenaa"
    },
    "Emelyn": {
        "girlNames": ["Ema", "Emelyn", "Emma", "Emalyn", "Em", "Lyn", "Emlyn", "Emelynn", "Emmelynn", "Emalynn"],
        "ig_handle": ""
    },
    "Lali": {
        "girlNames": ["Laila", "Lainey", "Layla", "Lara", "Larissa", "Lavinia", "Lauren", "Laurel", "Lauryn", "Lauran", "Laverne", "Lana", "Laney", "Latoya", "Latasha", "Latrice", "Lakisha", "Lakota", "Lakelyn", "Lacey", "Lace", "Laquita", "Laurine", "Laurena", "Laurisa", "Laurita", "Lauralee", "Lavera", "Lavonda", "Lania"],
        "ig_handle": ""
    }
}

# Define GPS and VPN pairs
GPS_VPN_pairs = [
  {"GPSlocation": "Zurich", "VPNlocation": "Zurich"},
  {"GPSlocation": "London", "VPNlocation": "London"},
  {"GPSlocation": "Stuttgart", "VPNlocation": "Stuttgart"},
  {"GPSlocation": "Dortmund", "VPNlocation": "Dortmund"},
  {"GPSlocation": "Berlin", "VPNlocation": "Berlin"},
  {"GPSlocation": "Minneapolis", "VPNlocation": "Chicago"},
  {"GPSlocation": "Fort Worth", "VPNlocation": "Dallas"},
  {"GPSlocation": "Quebec City", "VPNlocation": "Montreal"},
  {"GPSlocation": "Colorado Springs", "VPNlocation": "Denver"},
  {"GPSlocation": "Toledo", "VPNlocation": "Detroit"},
  {"GPSlocation": "Austin", "VPNlocation": "Houston"},
  {"GPSlocation": "Sacramento", "VPNlocation": "Los Angeles"},
  {"GPSlocation": "Laredo", "VPNlocation": "McAllen"},
  {"GPSlocation": "Orlando", "VPNlocation": "Miami"},
  {"GPSlocation": "Philadelphia", "VPNlocation": "New York"},
  {"GPSlocation": "Tucson", "VPNlocation": "Phoenix"},
  {"GPSlocation": "Charleston", "VPNlocation": "Raleigh"},
  {"GPSlocation": "Las Vegas", "VPNlocation": "Salt Lake City"},
  {"GPSlocation": "Fresno", "VPNlocation": "San Jose"},
  {"GPSlocation": "Spokane", "VPNlocation": "Seattle"},
  {"GPSlocation": "Sydney", "VPNlocation": "Sydney"},
  {"GPSlocation": "Melbourne", "VPNlocation": "Melbourne"},
  {"GPSlocation": "Brisbane", "VPNlocation": "Brisbane"},
  {"GPSlocation": "Perth", "VPNlocation": "Perth"},
  {"GPSlocation": "Adelaide", "VPNlocation": "Adelaide"},
  {"GPSlocation": "Baltimore", "VPNlocation": "Secaucus"},
  {"GPSlocation": "Bunbury", "VPNlocation": "Perth"},
  {"GPSlocation": "Hobart", "VPNlocation": "Melbourne"},
  {"GPSlocation": "Wollongong", "VPNlocation": "Sydney"},
  {"GPSlocation": "Rockhampton", "VPNlocation": "Brisbane"},
  {"GPSlocation": "Edmonton", "VPNlocation": "Calgary"},
  {"GPSlocation": "Ottawa", "VPNlocation": "Toronto"},
  {"GPSlocation": "Kelowna", "VPNlocation": "Vancouver"},
  {"GPSlocation": "Denver", "VPNlocation": "Denver"},
  {"GPSlocation": "Detroit", "VPNlocation": "Detroit"},
  {"GPSlocation": "Houston", "VPNlocation": "Houston"},
  {"GPSlocation": "Los Angeles", "VPNlocation": "Los Angeles"},
  {"GPSlocation": "McAllen", "VPNlocation": "McAllen"},
  {"GPSlocation": "Mount Gambier", "VPNlocation": "Adelaide"},
  {"GPSlocation": "Hamilton", "VPNlocation": "Auckland"},
  {"GPSlocation": "Edinburgh", "VPNlocation": "Glasgow"},
  {"GPSlocation": "Leeds", "VPNlocation": "London"},
  {"GPSlocation": "Sheffield", "VPNlocation": "Manchester"},
  {"GPSlocation": "Belfast", "VPNlocation": "Dublin"},
  {"GPSlocation": "Lausanne", "VPNlocation": "Zurich"},
  {"GPSlocation": "Graz", "VPNlocation": "Vienna"},
  {"GPSlocation": "Antwerp", "VPNlocation": "Brussels"},
  {"GPSlocation": "Nantes", "VPNlocation": "Bordeaux"},
  {"GPSlocation": "Nice", "VPNlocation": "Marseille"},
  {"GPSlocation": "Strasbourg", "VPNlocation": "Paris"},
  {"GPSlocation": "Leipzig", "VPNlocation": "Berlin"},
  {"GPSlocation": "Bonn", "VPNlocation": "Dusseldorf"},
  {"GPSlocation": "Nuremberg", "VPNlocation": "Frankfurt"},
  {"GPSlocation": "Turin", "VPNlocation": "Milan"},
  {"GPSlocation": "Catania", "VPNlocation": "Palermo"},
  {"GPSlocation": "Utrecht", "VPNlocation": "Amsterdam"},
  {"GPSlocation": "Porto", "VPNlocation": "Lisbon"},
  {"GPSlocation": "Bilbao", "VPNlocation": "Barcelona"},
  {"GPSlocation": "Seville", "VPNlocation": "Valencia"},
  {"GPSlocation": "Zaragoza", "VPNlocation": "Madrid"},
  {"GPSlocation": "Stockholm", "VPNlocation": "Gothenburg"},
  {"GPSlocation": "Copenhagen", "VPNlocation": "Malmö"},
  {"GPSlocation": "Tampere", "VPNlocation": "Helsinki"},
  {"GPSlocation": "Bergen", "VPNlocation": "Oslo"},
  {"GPSlocation": "Kristiansand", "VPNlocation": "Stavanger"},
  {"GPSlocation": "Aarhus", "VPNlocation": "Copenhagen"}
]

# Input email list (email:password format)
email_input = """
<EMAIL>:A3kX4oCcEx
<EMAIL>:WrlwwXsBUd
<EMAIL>:QmmB4a0zHv
<EMAIL>:jX1VOhXtMr
<EMAIL>:PJ9nvGrS8N
<EMAIL>:CfwPDSyaha
<EMAIL>:EExFR41e5W
<EMAIL>:LysxIXXZ8U
"""  # Add more emails as needed

# Parse the email input
emails = parse_emails(email_input)

# Extensive job titles and workplace combinations
job_workplace_combinations = [
    # Creative & Design
    {"job": "Graphic Designer", "workplace": "Creative Studio Berlin"},
    {"job": "UX Designer", "workplace": "Tech Hub London"},
    {"job": "Fashion Designer", "workplace": "Atelier Zurich"},
    {"job": "Interior Designer", "workplace": "Design House Stuttgart"},
    {"job": "Photographer", "workplace": "Photography Studio Dortmund"},
    {"job": "Art Director", "workplace": "Advertising Agency Berlin"},
    {"job": "Web Designer", "workplace": "Digital Agency London"},
    {"job": "Brand Designer", "workplace": "Marketing Firm Zurich"},

    # Marketing & Communications
    {"job": "Marketing Manager", "workplace": "Global Marketing Berlin"},
    {"job": "Social Media Manager", "workplace": "Influencer Agency London"},
    {"job": "Content Creator", "workplace": "Media Company Zurich"},
    {"job": "PR Specialist", "workplace": "Communications Stuttgart"},
    {"job": "Digital Marketer", "workplace": "Online Marketing Dortmund"},
    {"job": "Brand Manager", "workplace": "Consumer Goods Berlin"},
    {"job": "Communications Coordinator", "workplace": "Corporate London"},

    # Healthcare & Wellness
    {"job": "Nurse", "workplace": "City Hospital Berlin"},
    {"job": "Physical Therapist", "workplace": "Wellness Center London"},
    {"job": "Nutritionist", "workplace": "Health Clinic Zurich"},
    {"job": "Yoga Instructor", "workplace": "Fitness Studio Stuttgart"},
    {"job": "Dental Hygienist", "workplace": "Dental Practice Dortmund"},
    {"job": "Massage Therapist", "workplace": "Spa Resort Berlin"},

    # Education & Training
    {"job": "Elementary Teacher", "workplace": "International School London"},
    {"job": "Language Tutor", "workplace": "Language Center Zurich"},
    {"job": "Kindergarten Teacher", "workplace": "Montessori School Stuttgart"},
    {"job": "Dance Instructor", "workplace": "Dance Academy Dortmund"},
    {"job": "Art Teacher", "workplace": "Creative School Berlin"},

    # Business & Finance
    {"job": "Business Analyst", "workplace": "Consulting Firm London"},
    {"job": "Project Manager", "workplace": "Tech Company Zurich"},
    {"job": "HR Coordinator", "workplace": "Corporate Stuttgart"},
    {"job": "Sales Representative", "workplace": "Fashion Brand Dortmund"},
    {"job": "Account Manager", "workplace": "Client Services Berlin"},
    {"job": "Financial Advisor", "workplace": "Investment Firm London"},

    # Hospitality & Service
    {"job": "Event Planner", "workplace": "Event Agency Zurich"},
    {"job": "Hotel Manager", "workplace": "Boutique Hotel Stuttgart"},
    {"job": "Restaurant Manager", "workplace": "Fine Dining Dortmund"},
    {"job": "Travel Consultant", "workplace": "Travel Agency Berlin"},
    {"job": "Customer Success Manager", "workplace": "SaaS Company London"},

    # Fashion & Beauty
    {"job": "Fashion Stylist", "workplace": "Fashion House Zurich"},
    {"job": "Makeup Artist", "workplace": "Beauty Studio Stuttgart"},
    {"job": "Personal Shopper", "workplace": "Luxury Boutique Dortmund"},
    {"job": "Beauty Consultant", "workplace": "Cosmetics Brand Berlin"},
    {"job": "Fashion Buyer", "workplace": "Retail Chain London"},

    # Tech & Digital
    {"job": "Product Manager", "workplace": "Startup Zurich"},
    {"job": "Data Analyst", "workplace": "Analytics Firm Berlin"},
    {"job": "Software Tester", "workplace": "Tech Company London"},
    {"job": "Digital Consultant", "workplace": "Consulting Stuttgart"},
    {"job": "App Developer", "workplace": "Mobile Agency Dortmund"},
]

# High schools by location
location_high_schools = {
    "Zurich": [
        "Kantonsschule Zürich Nord",
        "Gymnasium Rämibühl",
        "Kantonsschule Hottingen",
        "Gymnasium Freudenberg",
        "Kantonsschule Wiedikon",
        "Gymnasium Unterstrass",
        "International School of Zurich",
        "Kantonsschule Rychenberg",
        "Gymnasium Immensee",
        "Kantonsschule Limmat"
    ],
    "London": [
        "Westminster School",
        "St. Paul's Girls' School",
        "Godolphin and Latymer School",
        "Francis Holland School",
        "Queen's Gate School",
        "More House School",
        "Putney High School",
        "Wimbledon High School",
        "Streatham & Clapham High School",
        "James Allen's Girls' School",
        "Dulwich College",
        "Alleyn's School",
        "City of London School for Girls",
        "South Hampstead High School",
        "Channing School"
    ],
    "Stuttgart": [
        "Gymnasium Stuttgart-Bad Cannstatt",
        "Königin-Charlotte-Gymnasium",
        "Eberhard-Ludwigs-Gymnasium",
        "Karls-Gymnasium Stuttgart",
        "Gymnasium Stuttgart-Degerloch",
        "Hölderlin-Gymnasium",
        "Zeppelin-Gymnasium",
        "Gottlieb-Daimler-Gymnasium",
        "Gymnasium Stuttgart-Vaihingen",
        "International School of Stuttgart"
    ],
    "Dortmund": [
        "Gymnasium an der Schweizer Allee",
        "Mallinckrodt-Gymnasium",
        "Reinoldus- und Schiller-Gymnasium",
        "Helmholtz-Gymnasium",
        "Immanuel-Kant-Gymnasium",
        "Max-Planck-Gymnasium",
        "Goethe-Gymnasium",
        "Phoenix-Gymnasium",
        "Stadtgymnasium Dortmund",
        "Heinrich-Heine-Gymnasium"
    ],
    "Berlin": [
        "John-F.-Kennedy-Schule",
        "Französisches Gymnasium",
        "Canisius-Kolleg",
        "Gymnasium Steglitz",
        "Rosa-Luxemburg-Gymnasium",
        "Heinrich-Hertz-Gymnasium",
        "Lessing-Gymnasium",
        "Goethe-Gymnasium",
        "Schiller-Gymnasium",
        "Beethoven-Gymnasium",
        "Berlin International School",
        "Gymnasium Tiergarten",
        "Sophie-Charlotte-Gymnasium",
        "Dreilinden-Gymnasium",
        "Paulsen-Gymnasium"
    ],
    "Sydney": [
        "Sydney Grammar School",
        "SCEGGS Darlinghurst",
        "Abbotsleigh School",
        "Pymble Ladies' College",
        "Knox Grammar School",
        "Barker College",
        "MLC School",
        "Kincoppal-Rose Bay",
        "Sydney Church of England Grammar School",
        "Loreto Kirribilli",
        "North Sydney Girls High School",
        "James Ruse Agricultural High School",
        "Sydney Boys High School",
        "Sydney Girls High School",
        "Fort Street High School"
    ],
    "Melbourne": [
        "Melbourne Grammar School",
        "Scotch College Melbourne",
        "Methodist Ladies' College",
        "Lauriston Girls' School",
        "Xavier College",
        "Trinity Grammar School",
        "Camberwell Grammar School",
        "Firbank Grammar School",
        "Haileybury College",
        "Caulfield Grammar School",
        "Melbourne High School",
        "Mac.Robertson Girls' High School",
        "University High School",
        "Balwyn High School",
        "Glen Waverley Secondary College"
    ],
    "Brisbane": [
        "Brisbane Grammar School",
        "Brisbane Girls Grammar School",
        "St Margaret's Anglican Girls School",
        "Somerville House",
        "All Hallows' School",
        "Churchie (Anglican Church Grammar School)",
        "St Joseph's College Gregory Terrace",
        "Stuartholme School",
        "Brisbane State High School",
        "Indooroopilly State High School",
        "Kelvin Grove State College",
        "Queensland Academy for Science Mathematics and Technology",
        "Cavendish Road State High School",
        "Clayfield College",
        "St Rita's College"
    ],
    "Perth": [
        "Hale School",
        "Christ Church Grammar School",
        "Perth Modern School",
        "Methodist Ladies' College Perth",
        "Presbyterian Ladies' College",
        "Scotch College Perth",
        "St Mary's Anglican Girls' School",
        "Penrhos College",
        "John Curtin College of the Arts",
        "Shenton College",
        "Rossmoyne Senior High School",
        "Applecross Senior High School",
        "Willetton Senior High School",
        "Mount Lawley Senior High School",
        "Churchlands Senior High School"
    ],
    "Adelaide": [
        "Prince Alfred College",
        "St Peter's College Adelaide",
        "Wilderness School",
        "Seymour College",
        "Pembroke School",
        "Pulteney Grammar School",
        "St Ignatius' College",
        "Mercedes College",
        "Adelaide High School",
        "Glenunga International High School",
        "Norwood Morialta High School",
        "Marryatville High School",
        "Brighton Secondary School",
        "Unley High School",
        "Mitcham Girls High School"
    ]
}

# Girly prompt answers for "All I ask is that you..."
girly_prompt_answers = [
    "bring me coffee in bed and I'll make you the best pancakes ever",
    "walk my dog with me and I'll cook you dinner every night",
    "watch rom-coms with me and I'll share my skincare routine 💕",
    "let me steal your hoodies and I'll bake you cookies weekly",
    "dance in the kitchen with me while we cook together",
    "give me forehead kisses and I'll be your biggest cheerleader",
    "hold my hand during scary movies and I'll plan the cutest dates",
    "let me do your skincare and I'll massage your shoulders after work",
    "sing car karaoke with me and I'll pack us the best picnic lunches",
    "cuddle me during thunderstorms and I'll make you laugh every day",
    "let me paint your nails and I'll teach you how to make my grandma's pasta",
    "take silly photos with me and I'll surprise you with your favorite treats",
    "let me pick your outfits sometimes and I'll always hype you up",
    "share your fries with me and I'll share my Netflix password forever",
    "let me braid your hair and I'll give you the best back rubs",
    "build blanket forts with me and I'll make us hot chocolate with marshmallows",
    "let me teach you TikTok dances and I'll learn your favorite video game",
    "go thrift shopping with me and I'll help you redecorate your place",
    "let me do your makeup for fun and I'll cook all your comfort foods",
    "have deep 2am conversations with me and I'll always listen to your dreams"
]

# Choose girl category (Irina, Sophie, Hellena, Emelyn, or Lali)
selected_category = "Lali"  # Change this to other model if needed

# Get girl names and Instagram handle for the selected category
girl_names = girl_names_and_handles[selected_category]["girlNames"]
ig_handle = girl_names_and_handles[selected_category]["ig_handle"]

# Check if there are enough GPS/VPN pairs for the emails
if len(emails) > len(GPS_VPN_pairs):
    raise ValueError("Not enough GPS and VPN pairs for the number of emails.")

# Generate iterations dynamically
iterations_data = []
for i in range(len(emails)):
    mail = emails[i]
    gps_vpn_pair = GPS_VPN_pairs[i]
    girl_name = random.choice(girl_names)
    
    iteration = {
        "containerName": generate_container_name(i + 1, gps_vpn_pair["GPSlocation"]),
        "GPSlocation": gps_vpn_pair["GPSlocation"],
        "VPNlocation": gps_vpn_pair["VPNlocation"],
        "girlName": girl_name,
        "emailAddress": mail["emailAddress"],
        "emailPassword": mail["emailPassword"],
        "minimumDate": "1975-01-01",  
        "maximumDate": "2000-12-31", 
        "hometown": gps_vpn_pair["GPSlocation"],
        "workplace": random.choice(job_workplace_combinations)["workplace"],
        "jobtitle": random.choice(job_workplace_combinations)["job"],
        "college": random.choice(location_high_schools.get(gps_vpn_pair["GPSlocation"], ["International High School"])),
        "promptAnswer1": random.choice(girly_prompt_answers),
        "promptAnswer2": random.choice([
        "midnight adventuressss 👹", "a lot of hair, wont even lie haha", "colourssss",
            "your favourite rollercoaster", "minecraft", "that secret level in your favorite video game", 
            "a cozy blanket fort with fairy lights", "a chaotic good energy boost ", 
            "a rainy night movie marathon", "a never-ending Sunday brunch ", 
            "unpacking a rare Pokémon card", "an all-access backstage pass", 
            "living in a Studio Ghibli movie", "a midnight snack raid", 
            "the best surprise road trip", "a festival headliner you can’t stop talking about", 
            "a mysterious new flavor of ice cream", "that song you keep replaying", 
            "owning a collection of too many cozy sweaters", "getting lost in a vintage bookstore", 
            "a scavenger hunt in the city", "a slow dance under string lights", 
            "a really good mystery novel you can’t put down", "that one friend who’s always up for spontaneous plans", 
            "a two-player game with infinite levels", "your favorite meme page in real life", 
            "a cliffhanger season finale", "an unexpected but perfect movie plot twist", 
            "the best coffee shop on a rainy day ☕", "your favorite childhood theme park", 
            "finding hidden treasures in a thrift store", "a rollercoaster but without safety belts", 
            "an all-you-can-eat sushi night", "a classic with a remix twist", 
            "a sun-soaked road trip playlist", "a colorful mural in a quiet alley", 
            "finding that perfect vinyl record", "a spontaneous midnight ice cream run", 
            "unraveling the mysteries of the universe together", "a new season of your comfort series", 
            "an unplanned dance party", "one too many movie quotes", "an unpredictable weather forecast", 
            "an unexplored city at night", "a jigsaw puzzle with a missing piece"]), # type: ignore
        "promptAnswer3": random.choice([
        "otters sleep while holding hands so they dont drift away :)",
            "my apples are interestingly prominent.",
            "there are more frog species than car brands",
            "i have proper apples (you interpret that (good luck with it))",
            "the angry German guy is still alive",
            "honey never spoils – they found pots of it in ancient Egyptian tombs!",
            "bananas are berries, but strawberries aren’t 🍓",
            "you can’t hum while holding your nose (try it!)",
            "sea otters have a favorite rock they keep in a pouch under their arms 🦦",
            "octopuses have three hearts ❤️",
            "a group of flamingos is called a ‘flamboyance’ 🌸",
            "the Eiffel Tower grows up to 6 inches in summer due to heat expansion",
            "kangaroos can’t walk backward",
            "butterflies taste with their feet",
            "a day on Venus is longer than a year on Venus",
            "some cats are allergic to humans 😹",
            "sloths can take up to a month to digest one meal",
            "a crocodile can’t stick its tongue out",
            "polar bears have black skin under their white fur 🐻‍❄️",
            "pineapples take about two years to grow 🍍",
            "wombat poop is cube-shaped",
            "koalas sleep up to 22 hours a day 😴",
            "chewing gum was once made from tree sap",
            "rabbits can’t vomit",
            "only female mosquitoes bite",
            "ants don’t have lungs, they breathe through tiny holes in their bodies",
            "the unicorn is the national animal of Scotland 🦄",
            "there’s a species of jellyfish that is technically immortal",
            "frogs use their eyes to help swallow food",
            "squirrels plant thousands of new trees each year by forgetting where they buried their acorns",
            "there’s a basketball court in the U.S. Supreme Court building 🏀",
            "your heartbeat syncs to the rhythm of the music you listen to 🎶",
            "a shrimp’s heart is located in its head",
            "a snail can sleep for three years 🐌",
            "the inventor of the Pringles can is now buried in one"]) # type: ignore
        } 
    
    iterations_data.append(iteration)

# Appium desired capabilities setup using XCUITestOptions
options = XCUITestOptions()
options.platformName = 'iOS'
options.platformVersion = '16.3.1'
options.deviceName = 'iPhone X'
options.udid = '363ac29f09c1d34afffb986e46656656fd7e651c'
options.automationName = 'XCUITest'
options.bundleId = 'co.hinge.mobile.ios'
options.xcodeOrgId = 'SHA.com'
options.xcodeSigningId = 'iPhone Developer'
options.set_capability('newCommandTimeout', 6000)
options.set_capability('platformVersion', '16.3.1')
options.set_capability('wdaConnectionTimeout', 60000)
options.set_capability('wdaLaunchTimeout', 60000)  
options.set_capability('showXcodeLog', True)
# options.set_capability('productBundleIdentifier', 'SHA.com.WebDriverAgentRnnrr')


# Prevent WebDriverAgent from being reset or uninstalled
# options.set_capability('noReset', True)

options.set_capability('useNewWDA', False)
options.set_capability('usePrebuiltWDA', True)

# Assign port 8103
options.set_capability('wdaLocalPort', 8111)

# Set up the Appium driver with host and port 127.0.0.1:7
driver = webdriver.Remote('http://127.0.0.1:4725', options=options)

# daisysms API variables
API_KEY = '26DizTweCqE0a4ZskbAFv2ShNqpuEE'
COUNTRY_CODE = '55'  # country number from daisysms API country list
MAX_PRICE = '0.6'

# Proxy rotation URL
PROXY_ROTATION_URL = 'https://i.fxdx.in/api-rt/changeip/RIDATjivGW/x65VKTSAP5WNP'

# Email API variables
EMAIL_API_TOKEN = '45qYH9xArudux8Ten3Veg8wur4XOLaDA'
EMAIL_API_BASE_URL = 'https://api.anymessage.shop'
EMAIL_SITE = 'hinge.co'
EMAIL_DOMAINS = 'gmail'  # Using gmail as requested

def create_driver_session():
    driver = webdriver.Remote('http://127.0.0.1:4725', options=options)
    return driver



def rotate_proxy_ip():
    """Rotate IP using the proxy rotation URL"""
    try:
        print("Rotating proxy IP...")
        response = requests.get(PROXY_ROTATION_URL)
        if response.status_code == 200:
            print("Proxy IP rotated successfully!")
            print(f"Response: {response.text}")
        else:
            print(f"Failed to rotate proxy IP. Status code: {response.status_code}")
        time.sleep(2)  # Wait a bit after rotation
    except Exception as e:
        print(f"Error rotating proxy IP: {e}")

def get_email_balance():
    """Get balance from email API"""
    try:
        url = f"{EMAIL_API_BASE_URL}/user/balance"
        params = {'token': EMAIL_API_TOKEN}
        response = requests.get(url, params=params, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                balance = data.get('balance')
                print(f"✅ Email API balance: {balance}")
                return float(balance)
            else:
                print(f"❌ Email API balance error: {data}")
                return None
        else:
            print(f"❌ Email API balance request failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error getting email balance: {e}")
        return None

def order_email(account_index=None):
    """Order a new email address for Hinge with cost tracking"""
    try:
        url = f"{EMAIL_API_BASE_URL}/email/order"
        params = {
            'token': EMAIL_API_TOKEN,
            'site': EMAIL_SITE,
            'domain': EMAIL_DOMAINS
        }
        response = requests.get(url, params=params, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                email_id = data.get('id')
                email_address = data.get('email')

                # anymessage.shop typically costs around $0.02-0.05 per email
                # We'll use $0.03 as a reasonable estimate
                email_cost = 0.03

                print(f"✅ Email ordered successfully: {email_address} (ID: {email_id})")

                # Log the cost if account_index is provided
                if account_index is not None:
                    account_logger.log_email_cost(account_index, email_address, email_cost)

                return email_id, email_address
            else:
                error_value = data.get('value', 'unknown error')
                print(f"❌ Email order error: {error_value}")
                return None, None
        else:
            print(f"❌ Email order request failed: {response.status_code}")
            return None, None
    except Exception as e:
        print(f"❌ Error ordering email: {e}")
        return None, None

def get_email_message(email_id, max_attempts=30, poll_delay=10):
    """Get email message by ID, polling until received"""
    try:
        url = f"{EMAIL_API_BASE_URL}/email/getmessage"
        params = {
            'token': EMAIL_API_TOKEN,
            'id': email_id
        }

        for attempt in range(max_attempts):
            print(f"Checking for email message (attempt {attempt + 1}/{max_attempts})...")
            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    message = data.get('message')
                    print(f"✅ Email message received!")
                    return message
                elif data.get('value') == 'wait message':
                    print(f"⏳ Email not received yet, waiting {poll_delay} seconds...")
                    time.sleep(poll_delay)
                    continue
                else:
                    print(f"❌ Email message error: {data}")
                    return None
            else:
                print(f"❌ Email message request failed: {response.status_code}")
                return None

        print(f"❌ Email message not received after {max_attempts} attempts")
        return None
    except Exception as e:
        print(f"❌ Error getting email message: {e}")
        return None

def extract_verification_code_from_email(html_content):
    """Extract verification code from Hinge email HTML"""
    try:
        import re
        from bs4 import BeautifulSoup

        # Parse HTML content
        soup = BeautifulSoup(html_content, 'html.parser')
        text_content = soup.get_text()

        # Common patterns for Hinge verification codes
        patterns = [
            r'verification code[:\s]*(\d{6})',
            r'code[:\s]*(\d{6})',
            r'(\d{6})',  # Any 6-digit number
            r'confirm[:\s]*(\d{6})',
            r'verify[:\s]*(\d{6})'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            if matches:
                code = matches[0]
                if len(code) == 6 and code.isdigit():
                    print(f"✅ Extracted verification code: {code}")
                    return code

        print("❌ Could not extract verification code from email")
        print(f"Email content preview: {text_content[:200]}...")
        return None

    except Exception as e:
        print(f"❌ Error extracting verification code: {e}")
        return None

def get_email_verification_code():
    """Complete flow: order email, get verification code"""
    try:
        # Check balance first
        balance = get_email_balance()
        if balance is None or balance <= 0:
            print("❌ Insufficient email API balance")
            return None, None

        # Order email
        email_id, email_address = order_email()
        if not email_id or not email_address:
            print("❌ Failed to order email")
            return None, None

        print(f"📧 Use this email address in Hinge: {email_address}")
        print(f"🔄 Waiting for verification email (ID: {email_id})...")

        # Wait for and get the email message
        html_message = get_email_message(email_id, max_attempts=30, poll_delay=10)
        if not html_message:
            print("❌ Failed to receive email message")
            return None, email_address

        # Extract verification code
        verification_code = extract_verification_code_from_email(html_message)
        if not verification_code:
            print("❌ Failed to extract verification code from email")
            return None, email_address

        return verification_code, email_address

    except Exception as e:
        print(f"❌ Error in email verification flow: {e}")
        return None, None

def enter_email_verification_code(driver, verification_code):
    """Enter email verification code in the email verification field"""
    try:
        wait = WebDriverWait(driver, 15)
        print("Waiting for email verification code field...")

        # Use the correct element locator from Appium Inspector
        email_code_field = wait.until(EC.presence_of_element_located((AppiumBy.XPATH, '//XCUIElementTypeOther[@name="Email verification code Text field"]')))
        email_code_field.clear()
        email_code_field.send_keys(verification_code)
        print(f"✅ Entered email verification code: {verification_code}")
        return True

    except TimeoutException:
        print("❌ Timeout waiting for email verification code field")
        return False
    except Exception as e:
        print(f"❌ Error entering email verification code: {e}")
        return False

def get_coordinates_for_location(location_name):
    """
    Get coordinates for a given location name.
    You'll need to populate this with the actual coordinates for each location.
    """
    # Coordinate mappings for locations
    coordinates_map = {
        # European locations for Lali model
        "Zurich": {"lat": 47.3769, "lng": 8.5417},
        "London": {"lat": 51.5074, "lng": -0.1278},
        "Stuttgart": {"lat": 48.7758, "lng": 9.1829},
        "Dortmund": {"lat": 51.5136, "lng": 7.4653},
        "Berlin": {"lat": 52.5200, "lng": 13.4050},

        # Previous locations
        "Ashburn": {"lat": 39.0438, "lng": -77.4874},
        "Augusta": {"lat": 33.4735, "lng": -82.0105},
        "Edmonton": {"lat": 53.5461, "lng": -113.4938},
        "Quebec City": {"lat": 46.8139, "lng": -71.2080},
        "Boston": {"lat": 42.3601, "lng": -71.0589},
        "Minneapolis": {"lat": 44.9778, "lng": -93.2650},
        "Fort Worth": {"lat": 32.7555, "lng": -97.3308},
        "Colorado Springs": {"lat": 38.8339, "lng": -104.8214},
        "Toledo": {"lat": 41.6528, "lng": -83.5379},
        "Austin": {"lat": 30.2672, "lng": -97.7431},
        "Sacramento": {"lat": 38.5816, "lng": -121.4944},
        "Orlando": {"lat": 28.5383, "lng": -81.3792},
        "Philadelphia": {"lat": 39.9526, "lng": -75.1652},
        "Tucson": {"lat": 32.2226, "lng": -110.9747},
        "Las Vegas": {"lat": 36.1699, "lng": -115.1398},
        "Fresno": {"lat": 36.7378, "lng": -119.7871},
        "Spokane": {"lat": 47.6588, "lng": -117.4260},

        # Australian main cities for Lali model
        "Sydney": {"lat": -33.8688, "lng": 151.2093},
        "Melbourne": {"lat": -37.8136, "lng": 144.9631},
        "Brisbane": {"lat": -27.4698, "lng": 153.0251},
        "Perth": {"lat": -31.9505, "lng": 115.8605},
        "Adelaide": {"lat": -34.9285, "lng": 138.6007},

        # Other Australian cities
        "Bunbury": {"lat": -33.3267, "lng": 115.6372},
        "Hobart": {"lat": -42.8821, "lng": 147.3272},
        "Wollongong": {"lat": -34.4278, "lng": 150.8931},
        "Rockhampton": {"lat": -23.3781, "lng": 150.5069},
        "Ottawa": {"lat": 45.4215, "lng": -75.6972},
        "Kelowna": {"lat": 49.8880, "lng": -119.4960},
        "Denver": {"lat": 39.7392, "lng": -104.9903},
        "McAllen": {"lat": 26.2034, "lng": -98.2300},
        "Hamilton": {"lat": -37.7870, "lng": 175.2793},
        # Add more locations as needed
    }

    return coordinates_map.get(location_name, {"lat": 0, "lng": 0})

def set_geranium_location(driver, location_name):
    """
    Set location in Geranium app using coordinates.
    """
    coordinates = get_coordinates_for_location(location_name)
    print(f"Setting Geranium location to {location_name}: {coordinates['lat']}, {coordinates['lng']}")

    try:
        wait = WebDriverWait(driver, 15)

        # Click LocSim button
        print("Waiting for LocSim button...")
        locsim_button = wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, '//XCUIElementTypeButton[@name="LocSim"]')))
        locsim_button.click()
        print("✅ Clicked LocSim button")

        # Click Map Pin button
        print("Waiting for Map Pin button...")
        map_pin_button = wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, '//XCUIElementTypeButton[@name="Map Pin"]')))
        map_pin_button.click()
        print("✅ Clicked Map Pin button")

        # Fill in latitude
        print("Waiting for latitude field...")
        latitude_field = wait.until(EC.presence_of_element_located((AppiumBy.XPATH, '//XCUIElementTypeTextField[@value="Latitude"]')))
        latitude_field.clear()
        latitude_field.send_keys(str(coordinates['lat']))
        print(f"✅ Entered latitude: {coordinates['lat']}")

        # Fill in longitude
        print("Waiting for longitude field...")
        longitude_field = wait.until(EC.presence_of_element_located((AppiumBy.XPATH, '//XCUIElementTypeTextField[@value="Longitude"]')))
        longitude_field.clear()
        longitude_field.send_keys(str(coordinates['lng']))
        print(f"✅ Entered longitude: {coordinates['lng']}")

        # Click OK button
        print("Waiting for OK button...")
        ok_button = wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, '//XCUIElementTypeButton[@name="OK"]')))
        ok_button.click()
        print("✅ Clicked OK button")

        print(f"✅ Successfully set Geranium location to {location_name}")
        return True

    except TimeoutException as e:
        print(f"❌ Timeout setting Geranium location: {e}")
        return False
    except Exception as e:
        print(f"❌ Error setting Geranium location: {e}")
        return False

# Generate random number between a minimum and maximum
def generate_random_number(min_value, max_value):
    return random.randint(min_value, max_value)

# Function to fetch the phone number from Daisy API with cost tracking
def get_phone_number(account_index=None):
    url = 'https://daisysms.com/stubs/handler_api.php'
    max_price = 0.40  # Cost per phone number
    data = {
        'api_key': API_KEY,  # Replace with your actual API key
        'action': 'getNumber',
        'service': 'vz',  # 'vz' for Hinge
        'max_price': max_price  # Adjust max price as needed
    }

    response = requests.get(url, params=data)
    response_data = response.text.split(':')

    # Print the full response for debugging
    print("API Response:", response_data)

    if response_data[0] == 'ACCESS_NUMBER':
        order_id = response_data[1]
        phone_number = response_data[2]

        # Remove leading '1' from the phone number if it starts with '1'
        if phone_number.startswith('1'):
            phone_number = phone_number[1:]

        print(f"Phone number purchased: {phone_number}")

        # Log the cost if account_index is provided
        if account_index is not None:
            account_logger.log_phone_cost(account_index, phone_number, max_price)

        return phone_number, order_id
    else:
        print("Failed to get phone number:", response_data[0])
        return None, None

# Function to poll for the SMS verification code
def get_verification_code(order_id):
    url = 'https://daisysms.com/stubs/handler_api.php'
    data = {
        'api_key': API_KEY,  # Replace with your actual API key
        'action': 'getStatus',
        'id': order_id
    }

    # Polling until the SMS is received
    for _ in range(10):  # Poll up to 10 times (adjust the number and delay as needed)
        response = requests.get(url, params=data)
        response_data = response.text.split(':')

        # Print the full response for debugging
        print("SMS Check Response:", response_data)

        if response_data[0] == 'STATUS_OK':
            sms_code = response_data[1]
            print(f"Received SMS code: {sms_code}")
            return sms_code
        elif response_data[0] == 'STATUS_WAIT_CODE':
            print("No SMS yet. Retrying in 10 seconds...")
            time.sleep(10)  # Wait for 10 seconds before retrying
        else:
            print(f"Failed to get SMS code: {response_data[0]}")
            break

    print("Failed to receive SMS.")
    # Optionally handle retries using Appium code here for resending the code
    return None

def enter_sms_code(driver, sms_code):
    # Ensure the code is 6 digits long
    if len(sms_code) != 6:
        print(f"Invalid SMS code length: {sms_code}")
        return

    # Enter SMS code in the single text field
    try:
        wait = WebDriverWait(driver, 15)
        print("Waiting for SMS code text field...")

        # Try the new single field format first
        try:
            sms_field = wait.until(EC.presence_of_element_located((AppiumBy.XPATH, '//XCUIElementTypeOther[@name="SMS code Text field"]')))
            sms_field.clear()
            sms_field.send_keys(sms_code)
            print(f"✅ SMS code {sms_code} entered successfully in single field!")
            return
        except TimeoutException:
            print("Single SMS field not found, trying individual digit fields...")

        # Fallback to individual digit fields if single field not found
        digit_fields = [
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[1]/XCUIElementTypeTextField",
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeTextField",
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeTextField",
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeTextField",
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeTextField",
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[6]/XCUIElementTypeTextField"
        ]

        for i, field_locator in enumerate(digit_fields):
            try:
                digit_field = wait.until(EC.presence_of_element_located((AppiumBy.IOS_CLASS_CHAIN, field_locator)))
                digit_field.clear()
                digit_field.send_keys(sms_code[i])
                print(f"✅ Entered digit {sms_code[i]} in field {i + 1}")
            except (TimeoutException, IndexError) as e:
                print(f"❌ Could not enter digit {i + 1}: {e}")
                return

        print(f"✅ SMS code {sms_code} entered successfully in individual fields!")

    except Exception as e:
        print(f"❌ Error during SMS code entry: {e}")
        return


# Perform touch action using W3C actions
def perform_touch_action(driver, x, y, hold_duration=0.1):
    try:
        print(f"Performing touch action at ({x}, {y}) with hold duration of {hold_duration} seconds.")
        touch = PointerInput(POINTER_TOUCH, "touch")
        actions = ActionChains(driver)
        actions.w3c_actions.pointer_action.move_to_location(x, y)
        actions.w3c_actions.pointer_action.pointer_down()
        actions.w3c_actions.pointer_action.pause(hold_duration)
        actions.w3c_actions.pointer_action.pointer_up()
        actions.perform()
        print(f"Touch action completed at ({x}, {y}).")
    except Exception as e:
        print(f"Error during touch action: {e}")

# Tap and hold at a specific location
def tap_and_hold(driver, x, y, hold_duration=2):
    try:
        touch = PointerInput(POINTER_TOUCH, "touch")
        actions = ActionChains(driver)
        print(f"Tapping and holding at ({x}, {y}) for {hold_duration} seconds.")
        actions.w3c_actions.pointer_action.move_to_location(x, y)
        actions.w3c_actions.pointer_action.pointer_down()
        actions.w3c_actions.pointer_action.pause(hold_duration)
        actions.w3c_actions.pointer_action.pointer_up()
        actions.perform()
        print(f"Hold and release completed at ({x}, {y}).")
    except Exception as e:
        print(f"Error during tap and hold: {e}")

def random_date(min_date_str, max_date_str):
    # Convert strings to datetime objects
    min_date = datetime.strptime(min_date_str, "%Y-%m-%d")
    max_date = datetime.strptime(max_date_str, "%Y-%m-%d")

    # Generate a random number of days between the two dates
    delta = max_date - min_date
    random_days = random.randint(0, delta.days)

    # Add the random number of days to the minimum date and return it formatted as DD MM YYYY
    return (min_date + timedelta(days=random_days)).strftime("%m %d %Y")

def extract_verification_code_from_subject_and_body(subject, body):
    # Example function to extract the verification code (assuming it's a 6-digit code)
    import re
    # Try to extract from subject first
    match = re.search(r'\d{6}', subject)
    if match:
        return match.group(0)
    
    # If not found in subject, try the body
    match = re.search(r'\d{6}', body)
    if match:
        return match.group(0)
    
    return None

def get_verification_code_from_email(emailAddress, emailPassword, max_attempts=1, poll_delay=10):
    print(emailAddress)
    print(emailPassword)
    for attempt in range(max_attempts):  # Poll up to max_attempts times
        try:
            print(f"Connecting to IMAP server for {emailAddress}...")
            mail = imaplib.IMAP4_SSL("imap.gmx.com", 993)  # Firstmail IMAP

            print(f"Logging in as {emailAddress}...")
            mail.login(emailAddress, emailPassword)
            print(f"Successfully logged in as {emailAddress}.")

            print(f"Selecting the inbox for {emailAddress}...")
            mail.select("inbox")

            print(f"Searching for emails with 'Verification' in the subject for {emailAddress}...")
            result, data = mail.search(None, '(SUBJECT "Verification")')

            if result != 'OK':
                print(f"Search failed with result: {result}")
                return None

            email_ids = data[0].split()

            if not email_ids:
                print(f"No verification emails found for {emailAddress}. Retrying in {poll_delay} seconds...")
                time.sleep(poll_delay)
                continue

            print(f"Found {len(email_ids)} email(s) for {emailAddress}. Fetching the latest one...")

            latest_email_id = email_ids[-1]
            result, msg_data = mail.fetch(latest_email_id, "(RFC822)")

            if result != 'OK':
                print(f"Failed to fetch email with result: {result}")
                return None

            # This is where the replacement block starts:
            for response_part in msg_data:
                if isinstance(response_part, tuple):
                    print(f"response_part: {response_part}")
                    print(f"type of response_part[1]: {type(response_part[1])}")

                    # If response_part[1] is not bytes, convert it
                    if isinstance(response_part[1], str):
                        response_part_bytes = response_part[1].encode('utf-8')
                        print("Converted response_part[1] to bytes")
                    else:
                        response_part_bytes = response_part[1]

                    # Now try parsing with message_from_bytes
                    try:
                        msg = email.message_from_bytes(response_part_bytes)
                        print("Email successfully parsed")
                    except Exception as e:
                        print(f"Error parsing email: {e}")
                        continue

                    email_subject = decode_header(msg["Subject"])[0][0]
                    if isinstance(email_subject, bytes):
                        email_subject = email_subject.decode()

                    print(f"Email subject: {email_subject}")

                    # Handle multipart emails
                    if msg.is_multipart():
                        for part in msg.walk():
                            if part.get_content_type() == "text/plain":
                                email_body = part.get_payload(decode=True).decode('utf-8', errors='replace')
                                print(f"Email body (text/plain): {email_body}")
                                break
                    else:
                        email_body = msg.get_payload(decode=True).decode('utf-8', errors='replace')
                        print(f"Email body: {email_body}")

                    # Extract verification code
                    verification_code_match = re.search(r'\b\d{6}\b', email_body)
                    if verification_code_match:
                        verification_code = verification_code_match.group(0)
                        print(f"Verification code found: {verification_code}")
                        return verification_code
                    else:
                        print("No verification code found")
                else:
                    print("Unexpected response format.")
            
        except Exception as e:
            print(f"Failed to retrieve email for {emailAddress}. Error: {e}")
            time.sleep(poll_delay)
            continue

        finally:
            print(f"Logging out of email for {emailAddress}.")
            mail.logout()

    print(f"Failed to receive verification code after {max_attempts} attempts.")
    return None

def extract_verification_code_from_subject_and_body(subject, body):
    """
    This is a placeholder function. You should implement logic here to
    extract the verification code from the email subject and body.
    """
    # Example logic to extract a 6-digit code from the subject/body
    import re
    match = re.search(r"\b\d{6}\b", subject + body)
    if match:
        return match.group(0)
    return None



def retrieve_and_enter_verification_code(driver, email_address, email_password, max_attempts=3, delay_between_attempts=10):
    attempts = 0
    code_resent = False  # Track if the code has been resent

    while attempts < max_attempts:
        # Check if the session is still active
        if driver.session_id is None:
            print("Session terminated. Reinitializing the driver session...")
            driver = create_driver_session()  # Replace with your session initialization code
            driver.activate_app("co.hinge.mobile.ios")  # Reopen the app

        # Try to get the verification code from the email
        print(f"Attempt {attempts + 1}/{max_attempts}: Checking for verification code...")
        verification_code = get_verification_code_from_email(email_address, email_password, max_attempts=1)

        if verification_code:
            print(f"Final Verification Code for {email_address}: {verification_code}")
            enter_email_verification_code(driver, verification_code)

            # Click the 'Next' button to proceed
            try:
                print("Clicking the Next button...")
                next_button = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                next_button.click()

                # Wait for 4 seconds after clicking 'Next' for the Date Input Field to appear
                print("Waiting for 15 seconds to check for the Date Input Field...")
                time.sleep(15)

                # Try to find the 'Date Input Field'
                date_input_field = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Date Input Field"`]')
                print("Date Input Field found. Proceeding...")
                return True  # Exit the function after successful verification and transition

            except Exception as e:
                print(f"Date Input Field not found or couldn't click Next. Restarting the app. Error: {e}")

                # Restart the app and session
                try:
                    driver.terminate_app('co.hinge.mobile.ios')
                    driver.activate_app('co.hinge.mobile.ios')
                    time.sleep(15)
                except Exception as e:
                    print(f"Error during app restart: {e}")
                    driver.quit()  # Terminate the session
                    driver = create_driver_session()  # Reinitialize the session
                    driver.activate_app("co.hinge.mobile.ios")  # Reopen the app

                print("App reopened. Re-entering email and verification code...")

                try:
                    # filling in the email account
                    el23 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "<EMAIL>"`]')
                    el23.send_keys(email_address)

                    time.sleep(3.7)

                    el24 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                    el24.click()

                    time.sleep(7.7)

                    #### LOGIN WITH OTHER EMAIL WORKFLOW

                    # Clicking "No Thanks" instead of logging in somewhere
                    el25 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "No thanks"`]')
                    el25.click()

                except Exception:
                    pass

                time.sleep(7.7)

                # el24 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                # el24.click()

                # time.sleep(7.7)
                print("Re-entering the verification code...")
                enter_email_verification_code(driver, verification_code)  # Re-enter the code

        else:
            print(f"Failed to retrieve verification code for {email_address}, attempt {attempts + 1}/{max_attempts}")

        # Wait before attempting again
        time.sleep(delay_between_attempts)
        attempts += 1

        # If attempts exceed max_attempts and code hasn't been resent, trigger the resend code logic
        if attempts >= max_attempts and not code_resent:
            print("Attempting to resend verification code after the maximum failed attempts.")
            try:
                trouble_verifying = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Having trouble verifying?"`]')
                trouble_verifying.click()
                print("Clicked on 'Having trouble verifying?'")

                time.sleep(7.7)

                resend_code = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send code again"`]')
                resend_code.click()
                print("Clicked on 'Send code again'")

                print("Verification code resent.")
                code_resent = True  # Mark that the code has been resent

                # Reset attempts to restart the process after resending the code
                attempts = 0
                print("Restarting verification code checking process after resending code.")

            except Exception as e:
                print(f"Couldn't resend the code: {e}")
                return False

    print(f"Verification code not retrieved after {max_attempts} attempts.")
    return False


def click_next_and_reopen_if_needed(driver, verification_code, emailAddress):
    # Try clicking "Next" button
    try:
        el39 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el39.click()
        time.sleep(7.7)  # Wait to see if the next screen loads

        # Check if the expected element on the next screen is present
        next_screen_element = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Date Input Field"`]')
        if next_screen_element:
            print("Next screen loaded successfully.")
            return True

    except Exception as e:
        print("Failed to find or click 'Next' button or load the next screen:", e)

    # If we reach here, it means "Next" didn't work, so fully close and reopen the app
    print("Closing and reopening the app due to failure to advance...")

    # Fully close and relaunch the app
    driver.close_app()
    time.sleep(2)  # Small delay to ensure the app fully closes
    driver.launch_app()
    time.sleep(7)  # Wait for app to relaunch fully

    # Resend the code without fetching again (using the already fetched `verification_code`)
    try:
        print(f"Resending verification code: {verification_code}")
        emailfield = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "<EMAIL>"`]')
        emailfield.send_keys(emailAddress)
        time.sleep(0.77)
        emailnext = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        emailnext.click()
        time.sleep(2.8)
        enter_email_verification_code(driver, verification_code)
        time.sleep(2)  # Give time for verification code to be processed
    except Exception as e:
        print("Failed to re-enter the verification code after reopening the app:", e)

    return False

def tap_random_times(driver, x, y, hold_duration=0.2):
    # Randomly choose between 1, 2, or 3 taps
    tap_count = random.choice([1, 2, 3])
    print(f"Tapping and holding {tap_count} time(s).")
    
    # Perform the tap and hold action the chosen number of times
    for _ in range(tap_count):
        tap_and_hold(driver, x, y, hold_duration)

def perform_randomized_image_selection(driver):
    # Define the tap actions (coordinates for selecting images) in a list
    image_selection_actions = [
        (72, 176, 0.05),
        (194, 176, 0.05),
        (335, 176, 0.05),
        (66, 284, 0.05),
        (208, 284, 0.05),
        (313, 284, 0.05)
    ]

    # Shuffle the actions to randomize the order
    random.shuffle(image_selection_actions)

    # Perform each tap action in the randomized order
    for x, y, hold_duration in image_selection_actions:
        tap_and_hold(driver, x, y, hold_duration)
        time.sleep(0.9)

def perform_randomized_image_selection4deletion(driver):
    # Define the tap actions (coordinates for selecting images) in a list
    image_selection_actions = [
        (72, 330, 0.05),
        (194, 330, 0.05),
        (335, 330, 0.05),
        (66, 471, 0.05),
        (208, 471, 0.05),
        (313, 471, 0.05)
    ]

    # Shuffle the actions to randomize the order
    random.shuffle(image_selection_actions)

    # Perform each tap action in the randomized order
    for x, y, hold_duration in image_selection_actions:
        tap_and_hold(driver, x, y, hold_duration)
        time.sleep(0.9)

def find_correct_sliders(driver):
    # Loop through possible values for both minimum and maximum between 18 and 38
    for min_value in range(18, 39):  # Values from 18 to 38
        for max_value in range(18, 39):
            try:
                # Try finding the minimum slider
                el_min = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeSlider[`value == "minimum: {min_value}"`]')
                print(f"Found the minimum slider with value: {min_value}")
                
                # Try finding the maximum slider
                el_max = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeSlider[`value == "maximum: {max_value}"`]')
                print(f"Found the maximum slider with value: {max_value}")

                # If both are found, return them
                return el_min, el_max

            except Exception as e:
                # If it doesn't find the slider, continue searching
                print(f"Combination {min_value} and {max_value} did not match. Trying next...")

    # If no matching sliders are found, raise an error
    raise Exception("No valid sliders found in the range 18 to 38.")

def scroll_down(driver):

    # Calculate swipe start and end points (middle of the screen horizontally)
    start_x = 234
    start_y = 499  
    end_y = 103    # End near the top

    # Perform the scroll using 'mobile: swipe' for iOS
    driver.execute_script('mobile: swipe', {
        'direction': 'up',
        'startX': start_x,
        'startY': start_y,
        'endX': start_x,
        'endY': end_y,
        'duration': 800  # Duration in milliseconds
    })


def scroll_and_find_element(driver, element_locator, max_scrolls=20):
    scroll_attempts = 0

    while scroll_attempts < max_scrolls:
        try:
            # Try to find the element using its locator
            element = driver.find_element(AppiumBy.IOS_CLASS_CHAIN, element_locator)
            
            # If found, click the element
            element.click()
            print("Element found and clicked.")
            return True

        except NoSuchElementException:
            # If the element is not found, scroll down
            print(f"Element not found. Scrolling down... Attempt {scroll_attempts + 1}/{max_scrolls}")
            scroll_down(driver)
            scroll_attempts += 1

    print("Element not found after maximum scroll attempts.")
    return False

# THE PROCESS
def main_process(driver, variables):
    # Start logging for this account creation attempt
    account_index = account_logger.start_account_creation(
        variables["containerName"],
        variables["GPSlocation"],
        variables["girlName"]
    )

    try:
        # Rotate proxy IP before starting the process
        rotate_proxy_ip()

        # Extract variables from the dictionary
        containerName = variables["containerName"]
        GPSlocation = variables["GPSlocation"]
        VPNlocation = variables["VPNlocation"]  # Keep for reference but won't be used with Mullvad
        girlName = variables["girlName"]
        emailAddress = variables["emailAddress"]
        emailPassword = variables["emailPassword"]
        minimumDate = variables["minimumDate"]
        maximumDate = variables["maximumDate"]
        hometown = variables["hometown"]
        workplace = variables["workplace"]
        jobtitle = variables["jobtitle"]
        college = variables["college"]
        promptAnswer1 = variables["promptAnswer1"]
        promptAnswer2 = variables["promptAnswer2"]
        promptAnswer3 = variables["promptAnswer3"]

        driver = webdriver.Remote('http://127.0.0.1:4725', options=options)

        driver.execute_script('mobile:pressButton', {"name": "home"})

        time.sleep(4)

        ## IN CASE OF IMAGE SPOOFINGç
        # Open Photos and delete latest 6 images
        print()
        el97 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Photos"`]')
        el97.click()

        driver.terminate_app('com.apple.mobileslideshow')
        driver.activate_app('com.apple.mobileslideshow')

        time.sleep(1.78)
        try:
            el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Albums"`]')
            el98.click()
            time.sleep(2.78)
        except Exception as e:
            pass

        try:
            el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Recents"`]')
            el98.click()
        except Exception as e:
            pass

        time.sleep(1.78)
        try:
            el99 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Select"`]')
            el99.click()
            if not el99:
                print("gay shit wuth pic deletion")
                return
        except Exception as e:
            el99 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Select"`]')
            el99.click()
            if not el99:
                print("gay shit wuth pic deletion")
                return

        time.sleep(2.78)
        perform_randomized_image_selection4deletion(driver)
        time.sleep(3.7)
        el100 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Delete"`]')
        el100.click()
        time.sleep(3.78)
        el101 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Delete 6 Photos"`]')
        el101.click()
        time.sleep(2.77)
        driver.execute_script('mobile:pressButton', {"name": "home"})

        time.sleep(2.77)

        # Tap and hold on the Hinge app (put standardly at right bottom pinned) and create new crane container
        tap_and_hold(driver, 324, 754, hold_duration=0.7)
        time.sleep(2.4)
        tap_and_hold(driver, 236, 668, hold_duration=0.05)
        time.sleep(2)
        scroll_and_find_element(driver, '**/XCUIElementTypeButton[`name == "New Container"`]', 20)
        tap_and_hold(driver, 280, 334, hold_duration=0.05)
        time.sleep(3)

        # Checking for name field, filling in new container name and creating the container
        try:
            print("Filling in the container name.")
            wait = WebDriverWait(driver, 15)

            # Send input to a text field
            print("Waiting for container name field...")
            container_name_field = wait.until(EC.presence_of_element_located((AppiumBy.IOS_CLASS_CHAIN, "**/XCUIElementTypeTextField")))
            container_name_field.clear()
            container_name_field.send_keys(containerName)
            print(f"✅ Entered container name: {containerName}")

            # Click "Create" button
            print("Waiting for Create button...")
            create_button = wait.until(EC.element_to_be_clickable((AppiumBy.ACCESSIBILITY_ID, "Create")))
            create_button.click()
            print("✅ Finalized container creation.")

        except TimeoutException as e:
            print(f"❌ Timeout during container creation: {e}")
            return
        except Exception as e:
            print(f"❌ Error during container creation: {e}")
            return

        # Go home to be sure, open Geranium
        driver.execute_script('mobile:pressButton', {"name": "home"})

        # Open Geranium app
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Geranium app...")
            geranium_app = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeIcon[`name == "Geranium"`]')))
            geranium_app.click()
            print("✅ Opened Geranium app")

            # set location using coordinates
            if not set_geranium_location(driver, GPSlocation):
                print("❌ Failed to set Geranium location")
                return

        except TimeoutException:
            print("❌ Timeout waiting for Geranium app")
            return
        except Exception as e:
            print(f"❌ Error opening Geranium app: {e}")
            return

        # back to home
        driver.execute_script('mobile:pressButton', {"name": "home"})

        time.sleep(3)

        # Rotate IP using proxy instead of Mullvad VPN
        rotate_proxy_ip()

        time.sleep(4)

        # Open Hinge app
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Hinge app...")
            hinge_app = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeIcon[`name == "Hinge"`]')))
            hinge_app.click()
            print("✅ Opened Hinge app")
        except TimeoutException:
            print("❌ Timeout waiting for Hinge app")
            return
        except Exception as e:
            print(f"❌ Error opening Hinge app: {e}")
            return

        # Step 2: Tap "Create account" button
        try:
            wait = WebDriverWait(driver, 30)
            print("Waiting for Create account button...")
            create_account_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Create account"`]')))
            create_account_button.click()
            print("✅ Clicked Create account button")
        except TimeoutException:
            print("❌ Timeout waiting for Create account button")
            return
        except Exception as e:
            print(f"❌ Error clicking Create account button: {e}")
            return

        try:
            # Step 1: Fetch phone number and order ID from sms API
            phone_number, orderid = get_phone_number(account_index)
            if phone_number is None or orderid is None:
                print("Could not fetch phone number. Exiting process.")
                account_logger.log_account_failure(account_index, "Failed to get phone number from DaisySMS")
                return

            time.sleep(5.7)

            # Step 3: Fill the phone number fetched from the API
            try:
                wait = WebDriverWait(driver, 15)
                print("Waiting for phone number field...")
                phone_field = wait.until(EC.presence_of_element_located((AppiumBy.IOS_CLASS_CHAIN, "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[1]/XCUIElementTypeOther/XCUIElementTypeOther[1]/XCUIElementTypeTextField")))
                phone_field.clear()
                phone_field.send_keys(phone_number)
                print(f"✅ Phone number {phone_number} entered successfully!")

                print("Waiting for Next button...")
                next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
                next_button.click()
                print("✅ Next button clicked successfully!")
            except TimeoutException:
                print("❌ Timeout waiting for phone number field or Next button")
                return
            except Exception as e:
                print(f"❌ Failed to enter phone number or click Next button. Error: {e}")
                return  # Terminate the process

        except Exception as e:
            print(f"Error during process: {e}")

        time.sleep(5.7)

        # Step 4: Poll for the SMS code from sms API
        sms_code = get_verification_code(orderid)
        if sms_code is None:
            print("Could not fetch SMS code. Sending again.")
            try:
                wait = WebDriverWait(driver, 10)
                print("Waiting for 'Didn't get a code?' button...")
                didntgetcode = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Didn\'t get a code?"`]')))
                didntgetcode.click()
                print("✅ Clicked 'Didn't get a code?'")

                time.sleep(7.7)

                print("Waiting for 'Send again' button...")
                sendagain = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Send again"`]')))
                sendagain.click()
                print("✅ Clicked 'Send again'")
            except TimeoutException:
                print("❌ Timeout waiting for resend code buttons")

        time.sleep(0.7)

        # Step 5: Enter the SMS code in 6 separate fields
        enter_sms_code(driver, sms_code)

        # Check for bad phone number (redirected to email verification)
        try:
            wait = WebDriverWait(driver, 5)
            email_code_element = wait.until(EC.presence_of_element_located((AppiumBy.XPATH, '//XCUIElementTypeStaticText[@name="Enter email code"]')))
            if email_code_element:
                print("🚨 BAD PHONE NUMBER DETECTED - Redirected to email verification!")

                # Log this bad phone number
                bad_phone_logger.log_bad_phone_number(
                    phone_number=phone_number,
                    order_id=orderid,
                    reason="Phone number redirected to email verification instead of basic info",
                    cost_lost=0.40,  # DaisySMS cost
                    container_name=containerName,
                    location=GPSlocation,
                    girl_name=girlName
                )

                # Also log as account failure
                account_logger.log_account_failure(account_index, f"Bad phone number - redirected to email verification: {phone_number}")

                print("❌ Terminating process due to bad phone number")
                return
        except TimeoutException:
            print("✅ No email code screen detected - phone number is good")
            pass  # Element not found, continue normally

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after SMS code...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after SMS code")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after SMS code")
            return

        # Clicking next to enter basic info
        try:
            wait = WebDriverWait(driver, 30)
            print("Waiting for 'Enter basic info' button...")
            try:
                enter_basic_info = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Enter basic info"`]')))
                enter_basic_info.click()
                print("✅ Clicked 'Enter basic info' button")
            except TimeoutException:
                print("Trying alternative 'Enter basic info' element...")
                enter_basic_info = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Enter basic info"`]')))
                enter_basic_info.click()
                print("✅ Clicked 'Enter basic info' text element")
        except TimeoutException:
            print("❌ Timeout waiting for 'Enter basic info' element")

            # Double-check if we're stuck on email verification (another bad phone number indicator)
            try:
                email_code_check = driver.find_element(AppiumBy.XPATH, '//XCUIElementTypeStaticText[@name="Enter email code"]')
                if email_code_check:
                    print("🚨 CONFIRMED BAD PHONE NUMBER - Still on email verification screen!")

                    # Log this bad phone number if not already logged
                    bad_phone_logger.log_bad_phone_number(
                        phone_number=phone_number,
                        order_id=orderid,
                        reason="Phone number stuck on email verification - no basic info button",
                        cost_lost=0.40,  # DaisySMS cost
                        container_name=containerName,
                        location=GPSlocation,
                        girl_name=girlName
                    )

                    account_logger.log_account_failure(account_index, f"Bad phone number - stuck on email verification: {phone_number}")
            except:
                pass  # Email code element not found

            return

        # Enter first name
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for first name field...")
            first_name_field = wait.until(EC.presence_of_element_located((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeTextField[`value == "First name (required)"`]')))
            first_name_field.send_keys(girlName)
            print(f"✅ Entered first name: {girlName}")
        except TimeoutException:
            print("❌ Timeout waiting for first name field")
            return

        # Click Next after first name
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after first name...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after first name")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after first name")
            return
        
        # Click "No Thanks" for login
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for 'No thanks' button...")
            no_thanks_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "No thanks"`]')))
            no_thanks_button.click()
            print("✅ Clicked 'No thanks' button")
        except TimeoutException:
            print("❌ Timeout waiting for 'No thanks' button")
            return

        # Get email from API first
        print("🔄 Getting email address from API...")
        email_id, api_email_address = order_email(account_index)
        if not email_id or not api_email_address:
            print("❌ Failed to get email from API")
            account_logger.log_account_failure(account_index, "Failed to get email from anymessage API")
            return

        print(f"📧 Using API email: {api_email_address}")

        # Enter email address
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for email field...")
            email_field = wait.until(EC.presence_of_element_located((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeTextField[`value == "<EMAIL>"`]')))
            email_field.send_keys(api_email_address)
            print(f"✅ Entered email: {api_email_address}")
        except TimeoutException:
            print("❌ Timeout waiting for email field")
            return

        # Click Next after email
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after email...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after email")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after email")
            return


        print(f'Starting email verification with API...')

        # Wait for and get the email message using the email_id from earlier
        print(f"🔄 Waiting for verification email (ID: {email_id})...")
        html_message = get_email_message(email_id, max_attempts=30, poll_delay=10)

        if html_message:
            # Extract verification code
            verification_code = extract_verification_code_from_email(html_message)

            if verification_code:
                print(f"✅ Got verification code: {verification_code}")

                # Enter the verification code
                if enter_email_verification_code(driver, verification_code):
                    # Click Next button
                    try:
                        wait = WebDriverWait(driver, 15)
                        print("Waiting for Next button after email verification...")
                        next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
                        next_button.click()
                        print("✅ Clicked Next button after email verification")
                    except TimeoutException:
                        print("❌ Timeout waiting for Next button after email verification")
                        return

                    time.sleep(7.7)
                else:
                    print("❌ Failed to enter email verification code")
                    return
            else:
                print("❌ Failed to extract verification code from email")
                return
        else:
            print("❌ Failed to receive verification email")
            return
        
        # Fill in randomized date, using minimum date and maximum date per model
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for date input field...")
            date_field = wait.until(EC.presence_of_element_located((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeOther[`name == "Date Input Field"`]')))
            random_birth_date = random_date(minimumDate, maximumDate)
            date_field.send_keys(random_birth_date)
            print(f"✅ Entered birth date: {random_birth_date}")
        except TimeoutException:
            print("❌ Timeout waiting for date input field")
            return

        # Click Next after date
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after date...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after date")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after date")
            return

        # Click Confirm
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Confirm button...")
            confirm_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Confirm"`]')))
            confirm_button.click()
            print("✅ Clicked Confirm button")
        except TimeoutException:
            print("❌ Timeout waiting for Confirm button")
            return

        time.sleep(7.7)

        # [IMPERFECT] tapping enable notifications and going next
        time.sleep(2)  # Keep for coordinate click
        tap_and_hold(driver, 133, 311, hold_duration=0.2)
        time.sleep(1.7)  # Keep for coordinate click

        # Click Next after notifications
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after notifications...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after notifications")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after notifications")
            return

        # Click "Add more details"
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for 'Add more details' button...")
            add_details_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Add more details"`]')))
            add_details_button.click()
            print("✅ Clicked 'Add more details' button")
        except TimeoutException:
            print("❌ Timeout waiting for 'Add more details' button")
            return

        # Click "Locate me" button
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for 'Locate me' button...")
            location_button = wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, '//XCUIElementTypeButton[@name="Locate me"]')))
            location_button.click()
            print("✅ Clicked 'Locate me' button")
        except TimeoutException:
            print("❌ Timeout waiting for 'Locate me' button")
            return

        # Click Next after location
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after location...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after location")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after location")
            return

        # Skip pronouns section
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button (skip pronouns)...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button (skip pronouns)")
        except TimeoutException:
            print("❌ Timeout waiting for Next button (skip pronouns)")
            return

        # Select Gender "Woman"
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for 'Woman' gender option...")
            woman_option = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Woman"`]')))
            woman_option.click()
            print("✅ Selected 'Woman' gender")

            print("Waiting for Next button after gender...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after gender")
        except TimeoutException:
            print("❌ Timeout waiting for gender selection")
            return

        # Select sexuality "Straight"
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for 'Straight' sexuality option...")
            straight_option = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Straight"`]')))
            straight_option.click()
            print("✅ Selected 'Straight' sexuality")

            print("Waiting for Next button after sexuality...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after sexuality")
        except TimeoutException:
            print("❌ Timeout waiting for sexuality selection")
            return

        # Select dating preference "Men"
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for 'Men' dating preference...")
            men_option = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Men"`]')))
            men_option.click()
            print("✅ Selected 'Men' dating preference")

            print("Waiting for Next button after dating preference...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after dating preference")
        except TimeoutException:
            print("❌ Timeout waiting for dating preference selection")
            return

        # Select dating intention "Figuring out my dating goals"
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for dating intention option...")
            dating_goals_option = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Figuring out my dating goals"`]')))
            dating_goals_option.click()
            print("✅ Selected 'Figuring out my dating goals'")

            print("Waiting for Next button after dating intention...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after dating intention")
        except TimeoutException:
            print("❌ Timeout waiting for dating intention selection")
            return


        # Select relationship type "Monogamy"
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for 'Monogamy' relationship type...")
            monogamy_option = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Monogamy"`]')))
            monogamy_option.click()
            print("✅ Selected 'Monogamy' relationship type")

            print("Waiting for Next button after relationship type...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after relationship type")
        except TimeoutException:
            print("❌ Timeout waiting for relationship type selection")
            return

        # Randomly change height and next
        time.sleep(2)  # Keep for coordinate click
        tap_random_times(driver, 186, 370, hold_duration=0.2)
        time.sleep(1)  # Keep for coordinate click

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after height...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after height")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after height")
            return

        # Change ethnicity (not visible)
        try:
            scroll_down(driver) #scrolling down to make it visible
            scroll_down(driver) #scrolling down to make it visible
            wait = WebDriverWait(driver, 15)
            print("Waiting for ethnicity option...")
            ethnicity_option = wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, '//*[contains(@name, "Caucasian") or contains(@name, "caucasian")]')))
            ethnicity_option.click()
            print("✅ Clicked ethnicity option")

            # time.sleep(1)  # Keep for coordinate click
            # tap_and_hold(driver, 337, 263, hold_duration=0.2)
            # time.sleep(1)  # Keep for coordinate click

            print("Waiting for Next button after ethnicity...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after ethnicity")
        except TimeoutException:
            print("❌ Timeout waiting for ethnicity options")
            return

        # Do you have children? - Select "Don't have kids"
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Waiting for children options to load...")

            # Wait for and click option that starts with "Don't"
            dont_have_kids_option = wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, '//XCUIElementTypeStaticText[starts-with(@name, "Don\'t")]')))
            dont_have_kids_option.click()
            print(f"✅ Selected children option: {dont_have_kids_option.get_attribute('name')}")

        except TimeoutException:
            print("❌ Timeout waiting for children options")
            return
        except Exception as e:
            print(f"❌ Error selecting children option: {e}")
            return

        time.sleep(1)

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after children question...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after children question")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after children question")
            return

        # Family plans - wait for options to load and click randomly
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Waiting for family plan options to load...")

            # Wait for any of the family plan options to be present
            family_plan_options = [
                '//XCUIElementTypeStaticText[starts-with(@name, "Don\'t")]',
                '//XCUIElementTypeStaticText[starts-with(@name, "Want")]',
                '//XCUIElementTypeStaticText[starts-with(@name, "Open")]',
                '//XCUIElementTypeStaticText[starts-with(@name, "Not")]',
                '//XCUIElementTypeStaticText[starts-with(@name, "Prefer")]'
            ]

            # Wait for at least one option to be present
            wait.until(EC.any_of(*[EC.presence_of_element_located((AppiumBy.XPATH, xpath)) for xpath in family_plan_options]))
            print("✅ Family plan options loaded")

            # Find all available options
            available_options = []
            for xpath in family_plan_options:
                try:
                    element = driver.find_element(AppiumBy.XPATH, xpath)
                    available_options.append(element)
                except:
                    continue

            if available_options:
                # Select a random option
                selected_option = random.choice(available_options)
                selected_option.click()
                print(f"✅ Randomly selected family plan option: {selected_option.get_attribute('name')}")
            else:
                print("❌ No family plan options found")

        except TimeoutException:
            print("❌ Timeout waiting for family plan options")
        except Exception as e:
            print(f"❌ Error selecting family plan option: {e}")

        time.sleep(1)

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after family plans...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after family plans")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after family plans")
            return

        # Skip Hometown (commented out)
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after hometown (skip)...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after hometown (skip)")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after hometown")
            return

        # Enter Workplace
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for workplace field...")
            workplace_field = wait.until(EC.presence_of_element_located((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeTextField[`value == "Workplace"`]')))
            workplace_field.send_keys(workplace)
            print(f"✅ Entered workplace: {workplace}")

            print("Waiting for Next button after workplace...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after workplace")
        except TimeoutException:
            print("❌ Timeout waiting for workplace field or Next button")
            return

        # Enter Job title
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for job title field...")
            job_title_field = wait.until(EC.presence_of_element_located((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeTextField[`value == "Job title"`]')))
            job_title_field.send_keys(jobtitle)
            print(f"✅ Entered job title: {jobtitle}")

            print("Waiting for Next button after job title...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after job title")
        except TimeoutException:
            print("❌ Timeout waiting for job title field or Next button")
            return

        # Skip College (commented out)
        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after college (skip)...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after college (skip)")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after college")
            return

        # Degree - select element containing "school"
        try:
            print("🔍 Waiting for degree options to load...")

            # Simple retry approach - try multiple times with fresh searches
            success = False
            for attempt in range(5):  # Try up to 5 times
                try:
                    time.sleep(1)  # Brief pause between attempts
                    school_elements = driver.find_elements(AppiumBy.XPATH, '//*[contains(@name, "school") or contains(@name, "School")]')

                    if school_elements:
                        # Try to click the first school element found
                        element = school_elements[0]
                        try:
                            element_name = element.get_attribute('name')
                            element.click()
                            print(f"✅ Selected degree option: {element_name}")
                            success = True
                            break
                        except:
                            # If getting name fails, just click without logging name
                            element.click()
                            print("✅ Selected degree option: (school option)")
                            success = True
                            break
                    else:
                        print(f"⚠️ Attempt {attempt + 1}: No school elements found, retrying...")

                except Exception as attempt_error:
                    print(f"⚠️ Attempt {attempt + 1} failed: {str(attempt_error)[:100]}...")
                    continue

            if not success:
                print("❌ Failed to select degree option after 5 attempts")
                return

        except Exception as e:
            print(f"❌ Error in degree selection: {e}")
            return

        time.sleep(1)

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after degree...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after degree")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after degree")
            return

        # Religious beliefs - randomly select between Christian, Catholic, Atheist, or Agnostic
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Waiting for religion options to load...")

            # Define religion names to look for
            religion_names = ["Christian", "Catholic", "Atheist", "Agnostic"]

            # Find and click religion option in one atomic operation
            def find_and_click_religion():
                # Look for any StaticText elements containing religion names
                all_elements = driver.find_elements(AppiumBy.XPATH, '//XCUIElementTypeStaticText')
                available_options = []

                for element in all_elements:
                    try:
                        element_name = element.get_attribute('name')
                        if element_name and any(religion in element_name for religion in religion_names):
                            available_options.append(element)
                    except:
                        continue

                if available_options:
                    selected_option = random.choice(available_options)
                    element_name = selected_option.get_attribute('name')
                    selected_option.click()
                    print(f"✅ Randomly selected religion: {element_name}")
                    return True
                return False

            # Wait for religion options to be available and click one
            wait.until(lambda driver: find_and_click_religion())

        except TimeoutException:
            print("❌ Timeout waiting for religion options")
        except Exception as e:
            print(f"❌ Error selecting religion option: {e}")

        time.sleep(1)

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after religious beliefs...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after religious beliefs")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after religious beliefs")
            return

        # Political beliefs - randomly select between Moderate, Not Political, Other, or Prefer not to say
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Waiting for political belief options to load...")

            # Define political belief options to choose from
            political_options = [
                '//XCUIElementTypeStaticText[@name="Moderate"]',
                '//XCUIElementTypeStaticText[@name="Not Political"]',
                '//XCUIElementTypeStaticText[@name="Other"]',
                '//XCUIElementTypeStaticText[@name="Prefer not to say"]'
            ]

            # Wait for at least one option to be present
            wait.until(EC.any_of(*[EC.presence_of_element_located((AppiumBy.XPATH, xpath)) for xpath in political_options]))
            print("✅ Political belief options loaded")

            # Find all available options
            available_options = []
            for xpath in political_options:
                try:
                    element = driver.find_element(AppiumBy.XPATH, xpath)
                    available_options.append(element)
                except:
                    continue

            if available_options:
                # Select a random option
                selected_option = random.choice(available_options)
                selected_option.click()
                print(f"✅ Randomly selected political belief: {selected_option.get_attribute('name')}")
            else:
                print("❌ No political belief options found")

        except TimeoutException:
            print("❌ Timeout waiting for political belief options")
        except Exception as e:
            print(f"❌ Error selecting political belief option: {e}")

        time.sleep(1)

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after political beliefs...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after political beliefs")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after political beliefs")
            return

        # Do you drink? - randomly select between Yes, Sometimes, No, or Prefer not to say
        try:
            print("🔍 Waiting for drinking options to load...")

            # Define drinking options to choose from
            drinking_names = ["Yes", "Sometimes", "No", "Prefer not to say"]

            # Simple retry approach - try multiple times with fresh searches
            success = False
            for attempt in range(5):  # Try up to 5 times
                try:
                    time.sleep(1)  # Brief pause between attempts
                    all_elements = driver.find_elements(AppiumBy.XPATH, '//XCUIElementTypeStaticText')
                    available_options = []

                    # Find elements matching drinking options
                    for element in all_elements:
                        try:
                            element_name = element.get_attribute('name')
                            if element_name and element_name in drinking_names:
                                available_options.append((element, element_name))
                        except:
                            continue

                    if available_options:
                        # Select a random option and click immediately
                        selected_element, selected_name = random.choice(available_options)
                        selected_element.click()
                        print(f"✅ Randomly selected drinking preference: {selected_name}")
                        success = True
                        break
                    else:
                        print(f"⚠️ Attempt {attempt + 1}: No drinking options found, retrying...")

                except Exception as attempt_error:
                    print(f"⚠️ Attempt {attempt + 1} failed: {str(attempt_error)[:100]}...")
                    continue

            if not success:
                print("❌ Failed to select drinking option after 5 attempts")

        except Exception as e:
            print(f"❌ Error in drinking selection: {e}")

        time.sleep(1)

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after drinking question...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after drinking question")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after drinking question")
            return

        # Do you smoke tobacco? - randomly select between Yes, Sometimes, No, or Prefer not to say
        try:
            print("🔍 Waiting for tobacco smoking options to load...")

            # Define tobacco smoking options to choose from
            tobacco_names = ["Yes", "Sometimes", "No", "Prefer not to say"]

            # Simple retry approach - try multiple times with fresh searches
            success = False
            for attempt in range(5):  # Try up to 5 times
                try:
                    time.sleep(1)  # Brief pause between attempts
                    all_elements = driver.find_elements(AppiumBy.XPATH, '//XCUIElementTypeStaticText')
                    available_options = []

                    # Find elements matching tobacco options
                    for element in all_elements:
                        try:
                            element_name = element.get_attribute('name')
                            if element_name and element_name in tobacco_names:
                                available_options.append((element, element_name))
                        except:
                            continue

                    if available_options:
                        # Select a random option and click immediately
                        selected_element, selected_name = random.choice(available_options)
                        selected_element.click()
                        print(f"✅ Randomly selected tobacco preference: {selected_name}")
                        success = True
                        break
                    else:
                        print(f"⚠️ Attempt {attempt + 1}: No tobacco options found, retrying...")

                except Exception as attempt_error:
                    print(f"⚠️ Attempt {attempt + 1} failed: {str(attempt_error)[:100]}...")
                    continue

            if not success:
                print("❌ Failed to select tobacco option after 5 attempts")

        except Exception as e:
            print(f"❌ Error in tobacco selection: {e}")

        time.sleep(1)

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after tobacco question...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after tobacco question")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after tobacco question")
            return

        # Do you smoke weed? - randomly select between Yes, Sometimes, No, or Prefer not to say
        try:
            print("🔍 Waiting for weed smoking options to load...")

            # Define weed smoking options to choose from
            weed_names = ["Yes", "Sometimes", "No", "Prefer not to say"]

            # Simple retry approach - try multiple times with fresh searches
            success = False
            for attempt in range(5):  # Try up to 5 times
                try:
                    time.sleep(1)  # Brief pause between attempts
                    all_elements = driver.find_elements(AppiumBy.XPATH, '//XCUIElementTypeStaticText')
                    available_options = []

                    # Find elements matching weed options
                    for element in all_elements:
                        try:
                            element_name = element.get_attribute('name')
                            if element_name and element_name in weed_names:
                                available_options.append((element, element_name))
                        except:
                            continue

                    if available_options:
                        # Select a random option and click immediately
                        selected_element, selected_name = random.choice(available_options)
                        selected_element.click()
                        print(f"✅ Randomly selected weed preference: {selected_name}")
                        success = True
                        break
                    else:
                        print(f"⚠️ Attempt {attempt + 1}: No weed options found, retrying...")

                except Exception as attempt_error:
                    print(f"⚠️ Attempt {attempt + 1} failed: {str(attempt_error)[:100]}...")
                    continue

            if not success:
                print("❌ Failed to select weed option after 5 attempts")

        except Exception as e:
            print(f"❌ Error in weed selection: {e}")

        time.sleep(1)

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after weed question...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after weed question")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after weed question")
            return

        # Do you use drugs? - randomly select between No or Prefer not to say
        try:
            print("🔍 Waiting for drug usage options to load...")

            # Define drug usage options to choose from (only No and Prefer not to say)
            drug_names = ["No", "Prefer not to say"]

            # Simple retry approach - try multiple times with fresh searches
            success = False
            for attempt in range(5):  # Try up to 5 times
                try:
                    time.sleep(1)  # Brief pause between attempts
                    all_elements = driver.find_elements(AppiumBy.XPATH, '//XCUIElementTypeStaticText')
                    available_options = []

                    # Find elements matching drug options
                    for element in all_elements:
                        try:
                            element_name = element.get_attribute('name')
                            if element_name and element_name in drug_names:
                                available_options.append((element, element_name))
                        except:
                            continue

                    if available_options:
                        # Select a random option and click immediately
                        selected_element, selected_name = random.choice(available_options)
                        selected_element.click()
                        print(f"✅ Randomly selected drug usage preference: {selected_name}")
                        success = True
                        break
                    else:
                        print(f"⚠️ Attempt {attempt + 1}: No drug options found, retrying...")

                except Exception as attempt_error:
                    print(f"⚠️ Attempt {attempt + 1} failed: {str(attempt_error)[:100]}...")
                    continue

            if not success:
                print("❌ Failed to select drug option after 5 attempts")

        except Exception as e:
            print(f"❌ Error in drug selection: {e}")

        time.sleep(1)

        try:
            wait = WebDriverWait(driver, 15)
            print("Waiting for Next button after drugs question...")
            next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            next_button.click()
            print("✅ Clicked Next button after drugs question")
        except TimeoutException:
            print("❌ Timeout waiting for Next button after drugs question")
            return

        time.sleep(7.7)

        # # check for "We value your privacy" screen
        # try:
        #     el877 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "I accept"`]')
        #     el877.click()
        #     el878 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        #     el878.click()

        # except Exception as e:
        #     print(f"No 'We value your privacy' screen")
        #     pass

        # Fill out your profile
        time.sleep(7.7)
        tap_and_hold(driver, 337, 320, hold_duration=0.2)
        el78 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Fill out your profile"`]')
        el78.click()

        # Add pictures
        time.sleep(5.7)
        el78 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeImage[`name == "mediaInputAddMedia4"`][1]')
        el78.click()
        time.sleep(1.77)
        # Wait for and click photo library/camera option
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Waiting for photo library options to load...")

            # Look for photo library options with multiple possible names
            library_options = [
                '//*[contains(@name, "Library")]',
                '//*[contains(@name, "library")]',
                '//*[contains(@name, "Camera Roll")]',
                '//*[contains(@name, "camera roll")]',
                '//*[contains(@name, "Photos")]',
                '//*[contains(@name, "photos")]'
            ]

            # Try each option until one is found (quick checks)
            el79 = None
            for xpath in library_options:
                try:
                    print(f"🔍 Trying to find element with xpath: {xpath}")
                    quick_wait = WebDriverWait(driver, 2)  # Only wait 2 seconds per option
                    el79 = quick_wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, xpath)))
                    print(f"✅ Found photo library option: {el79.get_attribute('name')}")
                    break
                except TimeoutException:
                    print(f"⚠️ Option not found: {xpath}")
                    continue

            if el79:
                el79.click()
                print("✅ Successfully clicked photo library option")
            else:
                print("❌ No photo library options found")
                return

        except Exception as e:
            print(f"❌ Error accessing photo library: {e}")
            return

        # Photos screen open, select
        print("📸 Photos screen opened, waiting for interface to stabilize...")
        time.sleep(9.75)

        try:
            print("🎯 Starting randomized image selection...")
            perform_randomized_image_selection(driver)
            print("✅ Image selection completed")
        except Exception as e:
            print(f"❌ Error during image selection: {e}")
            return

        # Click "Add" button with retry logic
        try:
            print("🔍 Looking for 'Add' button...")
            wait = WebDriverWait(driver, 10)
            add_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Add"`]')))
            add_button.click()
            print("✅ Successfully clicked 'Add' button")
        except TimeoutException:
            print("❌ Timeout waiting for 'Add' button")
            return
        except Exception as e:
            print(f"❌ Error clicking 'Add' button: {e}")
            return

        # Navigate through photo editing screens with robust error handling
        print("🔄 Starting photo editing navigation (6 photos total: 5x Next + 1x Done)...")
        time.sleep(7.7)

        # Click "Next" on first 5 photos (photos 1-5 of 6 total)
        for i in range(5):
            try:
                print(f"🔍 Looking for 'Next' button for photo {i + 1} of 6...")

                # Try multiple approaches to find the Next button
                next_button = None
                attempts = [
                    '**/XCUIElementTypeButton[`name == "Next"`]',
                    '//XCUIElementTypeButton[@name="Next"]',
                    '**/XCUIElementTypeButton[`label == "Next"`]'
                ]

                for attempt_xpath in attempts:
                    try:
                        wait = WebDriverWait(driver, 10)  # Increased timeout for reliability
                        next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN if attempt_xpath.startswith('**') else AppiumBy.XPATH, attempt_xpath)))
                        print(f"✅ Found 'Next' button for photo {i + 1} using: {attempt_xpath}")
                        break
                    except TimeoutException:
                        print(f"⚠️ 'Next' button not found with: {attempt_xpath}")
                        continue

                if next_button:
                    next_button.click()
                    print(f"✅ Successfully clicked 'Next' for photo {i + 1} of 6")
                    time.sleep(2)  # Brief pause between clicks
                else:
                    print(f"❌ CRITICAL: No 'Next' button found for photo {i + 1} - this should always exist!")
                    return  # This is a critical error since we expect 5 Next buttons

            except Exception as e:
                print(f"❌ CRITICAL ERROR with 'Next' button for photo {i + 1}: {e}")
                return  # Critical error - should not continue

        # Click "Done" button on the 6th (final) photo
        try:
            print("🔍 Looking for 'Done' button on photo 6 of 6...")
            wait = WebDriverWait(driver, 10)
            done_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Done"`]')))
            done_button.click()
            print("✅ Successfully clicked 'Done' button on final photo (6 of 6)")
        except TimeoutException:
            print("❌ CRITICAL: Timeout waiting for 'Done' button on final photo")
            return
        except Exception as e:
            print(f"❌ CRITICAL ERROR clicking 'Done' button on final photo: {e}")
            return

        # Click final "Next" button on Hinge interface
        try:
            print("🔍 Looking for final 'Next' button on Hinge interface...")
            wait = WebDriverWait(driver, 10)
            final_next_button = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]')))
            final_next_button.click()
            print("✅ Successfully clicked final 'Next' button")
        except TimeoutException:
            print("❌ Timeout waiting for final 'Next' button")
            return
        except Exception as e:
            print(f"❌ Error clicking final 'Next' button: {e}")
            return

        print("🎉 Photo selection and navigation completed successfully!")

        time.sleep(10.7)

        # Check for prompt screen
        try:
            print("🔍 Checking for the prompt screen...")
            missing_prompt_button = driver.find_element(by=AppiumBy.XPATH, value='//XCUIElementTypeButton[@name="Missing prompt 1. Select a Prompt and write your own answer."]')
            print("✅ Prompt screen found, continuing with the process...")
        except Exception as e:
            print(f"⚠️ Missing prompt button not found. Waiting and retrying. Error: {e}")
            time.sleep(10)

        time.sleep(7.7)

        # Setup Prompts Section
        print("📝 Starting prompt setup...")

        # Prompt no. 1
        try:
            print("🔍 Setting up Prompt #1...")
            el84 = driver.find_element(by=AppiumBy.XPATH, value='//XCUIElementTypeButton[@name="Missing prompt 1. Select a Prompt and write your own answer."]')
            el84.click()
            print("✅ Clicked Missing prompt 1 button")
            time.sleep(7.7)

            # Click View All
            print("🔍 Looking for 'View all' button...")
            el777 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "View all"`]')
            el777.click()
            print("✅ Clicked 'View all' button")
            time.sleep(7.7)

            # Select prompt question
            print("🔍 Selecting prompt question: 'All I ask is that you'...")
            el778 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "All I ask is that you"`]')
            el778.click()
            print("✅ Selected prompt question")
            time.sleep(3.7)

            # Click text field
            print("🔍 Clicking text field for prompt answer...")
            tap_and_hold(driver, 149, 223, 0.2)
            print("✅ Clicked text field")
            time.sleep(3.7)

            # Enter answer
            print(f"📝 Entering prompt answer: {promptAnswer1}")
            el85 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Be completely true to yourself."`]')
            el85.send_keys(promptAnswer1)
            print("✅ Entered prompt answer")
            time.sleep(3.7)

            # Click Done
            print("🔍 Clicking Done button...")
            el86 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]')
            el86.click()
            print("✅ Completed Prompt #1")
        except Exception as e:
            print(f"❌ Error setting up Prompt #1: {e}")
            return

        # Prompt no. 2 with robust socket hang up protection
        try:
            print("🔍 Setting up Prompt #2 with network resilience...")
            time.sleep(3.7)

            # Click Missing prompt 2 with robust error handling
            print("🔍 Looking for Missing prompt 2 button...")
            el87 = robust_find_element(driver, AppiumBy.XPATH, '//XCUIElementTypeButton[@name="Missing prompt 2. Select a Prompt and write your own answer."]')
            if not el87:
                print("❌ Failed to find Missing prompt 2 button after retries")
                return

            if not robust_click_element(driver, el87):
                print("❌ Failed to click Missing prompt 2 button after retries")
                return
            print("✅ Clicked Missing prompt 2 button")
            time.sleep(3.7)

            # Click View All with robust error handling
            print("🔍 Looking for 'View all' button...")
            el777 = robust_find_element(driver, AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "View all"`]')
            if not el777:
                print("❌ Failed to find 'View all' button after retries")
                return

            if not robust_click_element(driver, el777):
                print("❌ Failed to click 'View all' button after retries")
                return
            print("✅ Clicked 'View all' button")

            # Select prompt question with robust error handling
            print("🔍 Selecting prompt question: 'Dating me is like'...")
            el779 = robust_find_element(driver, AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Dating me is like"`]')
            if not el779:
                print("❌ Failed to find prompt question after retries")
                return

            if not robust_click_element(driver, el779):
                print("❌ Failed to click prompt question after retries")
                return
            print("✅ Selected prompt question")

            # Click text field (using coordinate tap - less prone to socket issues)
            print("🔍 Clicking text field for prompt answer...")
            tap_and_hold(driver, 149, 223, 0.2)
            print("✅ Clicked text field")
            time.sleep(3.7)

            # Enter answer with robust error handling
            print(f"📝 Entering prompt answer: {promptAnswer2}")
            el88 = robust_find_element(driver, AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Seeing a D-list celebrity at the airport. Mildly thrilling."`]')
            if not el88:
                print("❌ Failed to find text input field after retries")
                return

            if not robust_send_keys(driver, el88, promptAnswer2):
                print("❌ Failed to enter prompt answer after retries")
                return
            print("✅ Entered prompt answer")
            time.sleep(3.7)

            # Click Done with robust error handling
            print("🔍 Clicking Done button...")
            el89 = robust_find_element(driver, AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Done"`]')
            if not el89:
                print("❌ Failed to find Done button after retries")
                return

            if not robust_click_element(driver, el89):
                print("❌ Failed to click Done button after retries")
                return
            print("✅ Completed Prompt #2 successfully!")

        except Exception as e:
            print(f"❌ Unexpected error in Prompt #2 setup: {e}")
            return

        # Prompt no. 3
        try:
            print("🔍 Setting up Prompt #3...")
            time.sleep(3.7)
            el90 = driver.find_element(by=AppiumBy.XPATH, value='//XCUIElementTypeButton[@name="Missing prompt 3. Select a Prompt and write your own answer."]')
            el90.click()
            print("✅ Clicked Missing prompt 3 button")
            time.sleep(3.7)

            # Click View All
            print("🔍 Looking for 'View all' button...")
            el777 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "View all"`]')
            el777.click()
            print("✅ Clicked 'View all' button")

            # Select prompt question
            print("🔍 Selecting prompt question: 'A random fact I love is'...")
            el780 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "A random fact I love is"`]')
            el780.click()
            print("✅ Selected prompt question")

            # Click text field
            print("🔍 Clicking text field for prompt answer...")
            tap_and_hold(driver, 149, 223, 0.2)
            print("✅ Clicked text field")
            time.sleep(7.7)

            # Enter answer
            print(f"📝 Entering prompt answer: {promptAnswer3}")
            el91 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "It\'s illegal to own just one guinea pig in Switzerland because they get lonely."`]')
            el91.send_keys(promptAnswer3)
            print("✅ Entered prompt answer")
            time.sleep(7.7)

            # Click Done
            print("🔍 Clicking Done button...")
            el92 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]')
            el92.click()
            print("✅ Completed Prompt #3")
            time.sleep(7.7)
        except Exception as e:
            print(f"❌ Error setting up Prompt #3: {e}")
            return

        # Click Next to proceed
        try:
            print("🔍 Looking for first Next button...")
            el93 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
            el93.click()
            print("✅ Clicked first Next button")
            time.sleep(7.7)
        except Exception as e:
            print(f"❌ Error clicking first Next button: {e}")

        # Click second Next button
        try:
            print("🔍 Looking for second Next button...")
            el94 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
            el94.click()
            print("✅ Clicked second Next button")
            time.sleep(7.7)
        except Exception as e:
            print(f"❌ Error clicking second Next button: {e}")

        # Skip the upsell screen and the "All Done" screen
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Looking for 'Maybe later' button to skip upsell...")
            el95 = wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, '//XCUIElementTypeButton[@name="Maybe later"]')))
            el95.click()
            print("✅ Clicked 'Maybe later' button")
            time.sleep(3.7)
        except TimeoutException:
            print("❌ Timeout waiting for 'Maybe later' button")
        except Exception as e:
            print(f"❌ Error clicking 'Maybe later' button: {e}")

        # Start sending likes
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Looking for 'Start sending likes' button...")
            el96 = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Start sending likes"`]')))
            el96.click()
            print("✅ Clicked 'Start sending likes' button")

            # 🎉 ACCOUNT CREATION SUCCESS! Log it immediately
            account_logger.log_account_success(account_index)

        except TimeoutException:
            print("❌ Timeout waiting for 'Start sending likes' button")
            account_logger.log_account_failure(account_index, "Timeout waiting for 'Start sending likes' button")
            return
        except Exception as e:
            print(f"❌ Error clicking 'Start sending likes' button: {e}")
            account_logger.log_account_failure(account_index, f"Error clicking 'Start sending likes' button: {e}")
            return

        # Let it load and tap coordinates
        time.sleep(7.7)
        tap_and_hold(driver, 222, 222, 0.2)

        # Click discover tab
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Looking for discover tab...")
            el106 = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeImage[`name == "tabBarDiscoverDeselected"`]')))
            el106.click()
            print("✅ Clicked discover tab")
        except TimeoutException:
            print("❌ Timeout waiting for discover tab")

        # # Click OK, got it
        # try:
        #     wait = WebDriverWait(driver, 15)
        #     print("🔍 Looking for 'OK, got it' button...")
        #     el1065 = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "OK, got it"`]')))
        #     el1065.click()
        #     print("✅ Clicked 'OK, got it' button")
        # except TimeoutException:
        #     print("❌ Timeout waiting for 'OK, got it' button")

        # # Click Not now
        # try:
        #     wait = WebDriverWait(driver, 15)
        #     print("🔍 Looking for 'Not now' button...")
        #     el1066 = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Not now"`]')))
        #     el1066.click()
        #     print("✅ Clicked 'Not now' button")
        # except TimeoutException:
        #     print("❌ Timeout waiting for 'Not now' button")

        # Click Skip
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Looking for Skip button...")
            el107 = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Skip"`]')))
            el107.click()
            print("✅ Clicked Skip button")
        except TimeoutException:
            print("❌ Timeout waiting for Skip button")

        # Skip through profiles to complete onboarding
        print("⏭️ Starting to skip through profiles to complete onboarding...")

        for i in range(4):  # Try to skip up to 4 profiles
            try:
                wait = WebDriverWait(driver, 15)
                print(f"🔍 Looking for Skip button #{i+1}...")

                # Perform random swipes down (1 or 2 swipes) before clicking skip
                num_swipes = random.randint(1, 2)
                print(f"📱 Performing {num_swipes} random swipe(s) down...")

                for swipe in range(num_swipes):
                    try:
                        driver.swipe(start_x=200, start_y=600, end_x=200, end_y=300, duration=800)
                        print(f"✅ Completed swipe {swipe + 1}/{num_swipes}")
                        time.sleep(1)  # Short pause between swipes
                    except Exception as swipe_error:
                        print(f"⚠️ Error during swipe {swipe + 1}: {swipe_error}")

                # Look for any button that contains "Skip" in its name
                skip_button = wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, '//XCUIElementTypeButton[contains(@name, "Skip")]')))
                skip_button.click()
                print(f"✅ Clicked Skip button #{i+1}")

                # Wait 5 seconds between skips
                time.sleep(5)

            except TimeoutException:
                print(f"❌ No more Skip buttons found after {i} skips")
                break
            except Exception as e:
                print(f"❌ Error clicking Skip button #{i+1}: {e}")
                break

        print("✅ Completed skipping through profiles")

        time.sleep(3.7)


        # Click Dating preferences
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Looking for 'Dating preferences' button...")
            dating_prefs_button = wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, '//XCUIElementTypeButton[@name="Dating preferences"]')))
            dating_prefs_button.click()
            print("✅ Clicked 'Dating preferences' button")
        except TimeoutException:
            print("❌ Timeout waiting for 'Dating preferences' button")

        # Wait for and click Dealbreaker
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Waiting for 'Dealbreaker' option...")
            dealbreaker_option = wait.until(EC.presence_of_element_located((AppiumBy.XPATH, '//XCUIElementTypeStaticText[@name="Dealbreaker"]')))
            dealbreaker_option.click()
            print("✅ Clicked 'Dealbreaker' option")
        except TimeoutException:
            print("❌ Timeout waiting for 'Dealbreaker' option")

        # Adjust age slider with random value
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Looking for age slider...")
            age_slider = wait.until(EC.presence_of_element_located((AppiumBy.XPATH, '//XCUIElementTypeSlider[contains(@value, "maximum")]')))
            random_value = random.uniform(0.65, 0.8)
            age_slider.send_keys(random_value)
            print(f"✅ Adjusted age slider with value: {random_value:.3f}")
        except TimeoutException:
            print("❌ Timeout waiting for age slider")

        # Coordinate tap at (25, 68)
        print("🔍 Performing coordinate tap at (25, 68)...")
        tap_and_hold(driver, 25, 68, 0.14)
        print("✅ Completed coordinate tap")

        # Wait for and click close button
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Waiting for close button...")
            close_button = wait.until(EC.presence_of_element_located((AppiumBy.XPATH, '//XCUIElementTypeButton[@name="close"]')))
            close_button.click()
            print("✅ Clicked close button")
        except TimeoutException:
            print("❌ Timeout waiting for close button")

        # Click Skip buttons twice with 5 second waits
        print("⏭️ Clicking Skip buttons to complete sequence...")

        for i in range(2):
            try:
                wait = WebDriverWait(driver, 15)
                print(f"🔍 Looking for Skip button #{i+1}...")
                skip_button = wait.until(EC.element_to_be_clickable((AppiumBy.XPATH, '//XCUIElementTypeButton[contains(@name, "Skip")]')))
                skip_button.click()
                print(f"✅ Clicked Skip button #{i+1}")

                if i < 1:  # Don't wait after the last skip
                    print("⏳ Waiting 5 seconds...")
                    time.sleep(5)

            except TimeoutException:
                print(f"❌ Timeout waiting for Skip button #{i+1}")
                break
            except Exception as e:
                print(f"❌ Error clicking Skip button #{i+1}: {e}")
                break

        print("🎉 Completed final sequence!")

        # Click close button
        try:
            wait = WebDriverWait(driver, 15)
            print("🔍 Looking for close button...")
            el1055 = wait.until(EC.element_to_be_clickable((AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "close"`]')))
            el1055.click()
            print("✅ Clicked close button")
        except TimeoutException:
            print("❌ Timeout waiting for close button")

        time.sleep(3.7)

        driver.execute_script('mobile:pressButton', {"name": "home"})
        time.sleep(7.7)

    except Exception as e:
        print(f"Error during process for {containerName}: {e}")
        account_logger.log_account_failure(account_index, f"Unexpected error: {e}")


# Loop through the iterations_data list and call the main_process with each set of variables
print("🚀 Starting Hinge account creation automation...")
print(f"📊 Total accounts to create: {len(iterations_data)}")

for i, variables in enumerate(iterations_data):
    print(f"\n🔄 Starting process for iteration {i + 1}/{len(iterations_data)}")
    print(f"📍 Location: {variables['GPSlocation']}")
    print(f"👤 Name: {variables['girlName']}")

    main_process(driver, variables)

    print(f"✅ Completed process for iteration {i + 1}/{len(iterations_data)}")

# Print final session summary
print("\n" + "="*80)
print("🏁 AUTOMATION SESSION COMPLETED")
print("="*80)
account_logger.print_session_summary()
bad_phone_logger.print_bad_numbers_summary()

# End the session
# driver.quit()
