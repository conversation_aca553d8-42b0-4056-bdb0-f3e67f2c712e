import imaplib
import time

IMAP_SERVERS = {
    "gmx.com": "imap.gmx.com",
    "firstmail.ltd": "imap.firstmail.ltd",
    "veridicalmail.com": "imap.firstmail.ltd",
    "superocomail.com": "imap.firstmail.ltd",
    "velismail.com": "imap.firstmail.ltd",
    "reevalmail.com": "imap.firstmail.ltd"
}

def get_imap_server(email_address):
    domain = email_address.split("@")[-1]
    return IMAP_SERVERS.get(domain, None)

def check_email_for_keyword(email_address, email_password, keyword="Hinge", max_attempts=1):
    try:
        imap_server = get_imap_server(email_address)
        if not imap_server:
            print(f"No IMAP server configured for {email_address}. Skipping.")
            return False

        print(f"Connecting to IMAP server for {email_address} ({imap_server})...")
        mail = imaplib.IMAP4_SSL(imap_server, 993)

        print(f"Logging in as {email_address}...")
        mail.login(email_address, email_password)
        print("Successfully logged in.")

        print("Selecting the inbox...")
        mail.select("inbox")

        for attempt in range(max_attempts):
            print(f"Attempt {attempt + 1} to find emails with '{keyword}' in the subject.")
            result, data = mail.search(None, f'(SUBJECT "{keyword}")')

            if result == 'OK' and data[0].split():
                print(f"Keyword '{keyword}' found in an email for {email_address}.")
                mail.logout()
                return True
            else:
                print(f"No '{keyword}' emails found for {email_address}. Moving to the next account.")
                break

        mail.logout()
        return False

    except Exception as e:
        print(f"Error checking email for {email_address}: {e}")
        return False

def check_multiple_emails_from_file(file_path, keyword="Hinge"):
    with open("emails_without_keyword.txt", "w") as f:
        with open(file_path, "r") as file:
            for line in file:
                if line.strip():
                    email_address, email_password = line.strip().split(":")
                    if not check_email_for_keyword(email_address, email_password, keyword=keyword):
                        f.write(f"{email_address}:{email_password}\n")
                        f.flush()  # Ensure immediate write to file
                        print(f"{email_address}:{email_password} written to file as it lacks the keyword '{keyword}'.")

    print("All checks complete. Results saved to emails_without_keyword.txt")

# Example usage:
check_multiple_emails_from_file("email_credentials.txt", keyword="Hinge")
