#!/usr/bin/env python3
"""
Test script to verify the changes made to iX_hinge946new.py
"""

import requests
import time

# Proxy rotation URL
PROXY_ROTATION_URL = 'https://i.fxdx.in/api-rt/changeip/lnmrv4phq0/xK3E8MXNFBX9E'

def test_proxy_rotation():
    """Test the proxy rotation functionality"""
    try:
        print("Testing proxy rotation...")
        print(f"Making request to: {PROXY_ROTATION_URL}")
        
        response = requests.get(PROXY_ROTATION_URL, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Text: {response.text}")
        
        if response.status_code == 200:
            print("✅ Proxy rotation test successful!")
        else:
            print("❌ Proxy rotation test failed!")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error during proxy rotation test: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_daisysms_api():
    """Test the DaisySMS API with the new key"""
    API_KEY = 'qtlLKjcGPVol7qoth68ASvPWtKUpR7'
    
    try:
        print("\nTesting DaisySMS API...")
        url = 'https://daisysms.com/stubs/handler_api.php'
        data = {
            'api_key': API_KEY,
            'action': 'getBalance'
        }
        
        response = requests.get(url, params=data, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            if 'ACCESS_BALANCE' in response.text:
                print("✅ DaisySMS API test successful!")
            else:
                print("❌ DaisySMS API test failed - unexpected response")
        else:
            print("❌ DaisySMS API test failed!")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error during DaisySMS API test: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_coordinate_mapping():
    """Test the coordinate mapping function"""
    print("\nTesting coordinate mapping...")
    
    # Import the function from the updated file
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from iX_hinge946new import get_coordinates_for_location
        
        test_locations = ["Ashburn", "Boston", "Denver", "NonExistentCity"]
        
        for location in test_locations:
            coords = get_coordinates_for_location(location)
            print(f"{location}: {coords}")
            
        print("✅ Coordinate mapping test successful!")
        
    except ImportError as e:
        print(f"❌ Could not import coordinate function: {e}")
    except Exception as e:
        print(f"❌ Error during coordinate mapping test: {e}")

if __name__ == "__main__":
    print("🧪 Running tests for updated iX_hinge946new.py")
    print("=" * 50)
    
    test_proxy_rotation()
    test_daisysms_api()
    test_coordinate_mapping()
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")
    print("\nChanges applied to iX_hinge946new.py:")
    print("1. ✅ Updated DaisySMS API key")
    print("2. ✅ Added proxy rotation functionality")
    print("3. ✅ Replaced GPS Manager with Geranium")
    print("4. ✅ Replaced Mullvad VPN with proxy rotation")
    print("5. ✅ Added coordinate mapping for US/CA/AU/NZ locations")
    print("\nNext steps:")
    print("1. Implement Geranium coordinate input functionality")
    print("2. Test the full Hinge automation process")
