from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
from appium.options.ios import XCUITestOptions
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.actions.interaction import POINTER_TOUCH
from selenium.webdriver.common.actions.pointer_input import PointerInput
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.common.exceptions import NoSuchElementException
from datetime import datetime, timedelta
from email.header import decode_header
import random
import imaplib
import email
import re
import os
import base64
import re
import requests
import time

def generate_container_name(test_number, location):
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return f"Test {test_number} ({location}) - {current_time}"

# Function to parse emails in the format email:password
def parse_emails(email_string):
    emails = []
    lines = email_string.strip().split("\n")
    for line in lines:
        email, password = line.split(":")
        emails.append({"emailAddress": email, "emailPassword": password})
    return emails

# Define girl name categories and corresponding Instagram handles
girl_names_and_handles = {
    "<PERSON><PERSON>": {
        "girlNames": ["<PERSON>", "Ina", "Irina", "<PERSON><PERSON>", "Riri", "<PERSON>i", "I<PERSON>", "<PERSON>a", "Inabe<PERSON>"],
        "ig_handle": "i<PERSON>a.ivnv"
    },
    "<PERSON>": {
        "girlNames": ["<PERSON>", "<PERSON>", "<PERSON>a", "<PERSON>", "So", "Sof", "Soph", "<PERSON>e", "<PERSON>ph"],
        "ig_handle": "sophieelofiee"
    },
    "Hellena": {
        "girlNames": ["Hellen", "Lena", "Helen", "Helena", "Hella", "Leena", "Hel", "Helli"],
        "ig_handle": "hellibellidellii"
}}

# Define GPS and VPN pairs
GPS_VPN_pairs = [
    # {"GPSlocation": "Quebec city", "VPNlocation": "Montreal"},
    # {"GPSlocation": "Miami", "VPNlocation": "Miami"},
    # {"GPSlocation": "Mackay", "VPNlocation": "Brisbane"},
    # {"GPSlocation": "Portland", "VPNlocation": "Seattle"},
    # {"GPSlocation": "Washington", "VPNlocation": "Ashburn"},
    # {"GPSlocation": "Raleigh", "VPNlocation": "Atlanta"},
    # {"GPSlocation": "Pittsburgh", "VPNlocation": "Chicago"},
    # {"GPSlocation": "Syracuse", "VPNlocation": "New York"},
    # {"GPSlocation": "Boston", "VPNlocation": "Boston"},
    # {"GPSlocation": "Grand Rapids", "VPNlocation": "Chicago"},
    # {"GPSlocation": "Watford", "VPNlocation": "London"},
    # {"GPSlocation": "London CA", "VPNlocation": "Toronto"},
    # {"GPSlocation": "Halifax", "VPNlocation": "Vancouver"},
    # {"GPSlocation": "Ottawa", "VPNlocation": "Calgary"},
    # {"GPSlocation": "Sudbury", "VPNlocation": "Toronto"},
    # {"GPSlocation": "Sioux-Lookout", "VPNlocation": "Vancouver"},
    # {"GPSlocation": "Winnipeg", "VPNlocation": "Toronto"},
    # {"GPSlocation": "Canberra", "VPNlocation": "Melbourne"},
    # {"GPSlocation": "Gosford", "VPNlocation": "Sydney"},
    # {"GPSlocation": "Devonport", "VPNlocation": "Brisbane"},
    # {"GPSlocation": "Melbourne", "VPNlocation": "Sydney"},
    # {"GPSlocation": "Mount Gambier", "VPNlocation": "Adelaide"},
    # {"GPSlocation": "Adelaide", "VPNlocation": "Melbourne"},
    # {"GPSlocation": "Perth", "VPNlocation": "Sydney"},
    # {"GPSlocation": "Albany aus", "VPNlocation": "Brisbane"},
    # {"GPSlocation": "Darwin", "VPNlocation": "Perth"},
    # {"GPSlocation": "Gisborne", "VPNlocation": "Auckland"},
    # {"GPSlocation": "Wellington", "VPNlocation": "Auckland"},
    # {"GPSlocation": "Auckland", "VPNlocation": "Melbourne"},
    # {"GPSlocation": "Regina", "VPNlocation": "Calgary"},
    # {"GPSlocation": "Edmonton", "VPNlocation": "Vancouver"},
    # {"GPSlocation": "North Battleford", "VPNlocation": "Toronto"},
    # {"GPSlocation": "Dunedin", "VPNlocation": "Auckland"},
    # {"GPSlocation": "Christchurch", "VPNlocation": "Auckland"},
    # {"GPSlocation": "Kelowna", "VPNlocation": "Vancouver"},
    # {"GPSlocation": "Vancouver", "VPNlocation": "Montreal"},
    # {"GPSlocation": "Prince George", "VPNlocation": "Vancouver"},
    # {"GPSlocation": "Terrace Canada", "VPNlocation": "Toronto"},
    # {"GPSlocation": "st John’s", "VPNlocation": "Montreal"},
    # {"GPSlocation": "Frankfurt", "VPNlocation": "Frankfurt"},
    # {"GPSlocation": "Amsterdam", "VPNlocation": "Brussels"},
    # {"GPSlocation": "Leipzig", "VPNlocation": "Berlin"},
    # {"GPSlocation": "Hamburg", "VPNlocation": "Berlin"},
    # {"GPSlocation": "Zurich", "VPNlocation": "Vienna"},
    # {"GPSlocation": "Salzburg", "VPNlocation": "Zurich"},
    # {"GPSlocation": "Nice", "VPNlocation": "Marseille"},
    # {"GPSlocation": "Padova", "VPNlocation": "Milan"},
    # {"GPSlocation": "Wien", "VPNlocation": "Vienna"},
    # {"GPSlocation": "Settecamini", "VPNlocation": "Milan"},
    # {"GPSlocation": "Bari", "VPNlocation": "Palermo"},
    # {"GPSlocation": "Palermo", "VPNlocation": "Milan"},
    # {"GPSlocation": "Malaga", "VPNlocation": "Paris"},
    # {"GPSlocation": "Faro", "VPNlocation": "Bordeaux"},
    # {"GPSlocation": "Peniche", "VPNlocation": "Paris"},
    # {"GPSlocation": "Vigo", "VPNlocation": "Marseille"},
    {"GPSlocation": "San Francisco", "VPNlocation": "San Jose"},
    {"GPSlocation": "Los Angeles", "VPNlocation": "Ashburn"},
    {"GPSlocation": "Phoenix", "VPNlocation": "Los Angeles"},
    {"GPSlocation": "Boise", "VPNlocation": "Salt Lake City"},
    {"GPSlocation": "Seattle", "VPNlocation": "Seattle"},
    {"GPSlocation": "Salt Lake City", "VPNlocation": "Denver"},
    {"GPSlocation": "Denver", "VPNlocation": "Salt Lake City"},
    {"GPSlocation": "Albuquerque", "VPNlocation": "Phoenix"},
    {"GPSlocation": "Tulsa", "VPNlocation": "Dallas"},
    {"GPSlocation": "Memphis", "VPNlocation": "Atlanta"},
    {"GPSlocation": "Dallas", "VPNlocation": "Houston"},
    {"GPSlocation": "Houston", "VPNlocation": "McAllen"},
    {"GPSlocation": "San Antonio", "VPNlocation": "Dallas"},
    {"GPSlocation": "Baton Rouge", "VPNlocation": "Miami"},
    {"GPSlocation": "Atlanta", "VPNlocation": "Atlanta"},
    {"GPSlocation": "Jacksonville", "VPNlocation": "Miami"},
    {"GPSlocation": "Miami", "VPNlocation": "Miami"},
    {"GPSlocation": "New York", "VPNlocation": "New York"},
    {"GPSlocation": "Dayton", "VPNlocation": "Chicago"},
    {"GPSlocation": "Evansville", "VPNlocation": "Chicago"},
    {"GPSlocation": "Beckley", "VPNlocation": "Raleigh"},
    {"GPSlocation": "Bradenton FL", "VPNlocation": "Miami"},
    {"GPSlocation": "Dothan", "VPNlocation": "Atlanta"},
    {"GPSlocation": "Mobile", "VPNlocation": "Dallas"},
    {"GPSlocation": "Tuscaloosa", "VPNlocation": "Atlanta"},
    {"GPSlocation": "Peoria", "VPNlocation": "Chicago"},
    {"GPSlocation": "Jefferson City", "VPNlocation": "Dallas"},
    {"GPSlocation": "Topeka", "VPNlocation": "Dallas"},
    {"GPSlocation": "Des Moines", "VPNlocation": "Chicago"},
    {"GPSlocation": "Saint Paul", "VPNlocation": "Chicago"},
    {"GPSlocation": "Baraboo", "VPNlocation": "Chicago"},
    {"GPSlocation": "Marquette", "VPNlocation": "Detroit"},
    {"GPSlocation": "Columbus NE", "VPNlocation": "Denver"},
    {"GPSlocation": "Brookings", "VPNlocation": "Denver"},
    {"GPSlocation": "Midland TX", "VPNlocation": "Dallas"},
    {"GPSlocation": "Amarillo TX", "VPNlocation": "Dallas"},
    {"GPSlocation": "Las Vegas", "VPNlocation": "Phoenix"},
    {"GPSlocation": "Dodge City", "VPNlocation": "Denver"},
    {"GPSlocation": "Rapid City", "VPNlocation": "Denver"},
    {"GPSlocation": "North Platte", "VPNlocation": "Denver"},
    {"GPSlocation": "Fargo ND", "VPNlocation": "Chicago"},
    {"GPSlocation": "Bismarck ND", "VPNlocation": "Chicago"},
    {"GPSlocation": "Casper WY", "VPNlocation": "Denver"},
    {"GPSlocation": "Grand Junction CO", "VPNlocation": "Denver"},
    {"GPSlocation": "Trinidad CO", "VPNlocation": "Denver"},
    {"GPSlocation": "El Paso", "VPNlocation": "Phoenix"},
    {"GPSlocation": "Yuma", "VPNlocation": "Phoenix"},
    {"GPSlocation": "Sierra Vista", "VPNlocation": "Phoenix"},
    {"GPSlocation": "Page AZ", "VPNlocation": "Phoenix"},
    {"GPSlocation": "Hanford CA", "VPNlocation": "San Jose"},
    {"GPSlocation": "Reno NV", "VPNlocation": "San Jose"},
    {"GPSlocation": "Medford OR", "VPNlocation": "Seattle"},
    {"GPSlocation": "Jackson WY", "VPNlocation": "Salt Lake City"},
    {"GPSlocation": "Billings MT", "VPNlocation": "Salt Lake City"},
    {"GPSlocation": "Butte MT", "VPNlocation": "Salt Lake City"},
    {"GPSlocation": "Townsville", "VPNlocation": "Sydney"},
    {"GPSlocation": "Bundaberg", "VPNlocation": "Melbourne"},
    {"GPSlocation": "Brisbane", "VPNlocation": "Adelaide"},
    {"GPSlocation": "Coffs Harbour", "VPNlocation": "Perth"}
]

# Input email list (email:password format)
email_input = """
<EMAIL>:EvOhlnb93yl
<EMAIL>:lxf70pelkC74
<EMAIL>:p87s8O2cv
<EMAIL>:rS1vje2dVxo
<EMAIL>:vTF732iX5
<EMAIL>:B7jALlbmgq8
<EMAIL>:F407eeCJk2
<EMAIL>:k1s6fa21vBQ
<EMAIL>:ykYiBogk2i
<EMAIL>:R0kci7yTc
"""  # Add more emails as needed

# Parse the email input
emails = parse_emails(email_input)

# Choose girl category (Irina or Sophie)
selected_category = "Hellena"  # Change this to other model if needed

# Get girl names and Instagram handle for the selected category
girl_names = girl_names_and_handles[selected_category]["girlNames"]
ig_handle = girl_names_and_handles[selected_category]["ig_handle"]

# Check if there are enough GPS/VPN pairs for the emails
if len(emails) > len(GPS_VPN_pairs):
    raise ValueError("Not enough GPS and VPN pairs for the number of emails.")

# Generate iterations dynamically
iterations_data = []
for i in range(len(emails)):
    mail = emails[i]
    gps_vpn_pair = GPS_VPN_pairs[i]
    girl_name = random.choice(girl_names)
    
    iteration = {
        "containerName": generate_container_name(i + 1, gps_vpn_pair["GPSlocation"]),
        "GPSlocation": gps_vpn_pair["GPSlocation"],
        "VPNlocation": gps_vpn_pair["VPNlocation"],
        "girlName": girl_name,
        "emailAddress": mail["emailAddress"],
        "emailPassword": mail["emailPassword"],
        "minimumDate": "1975-01-01",  
        "maximumDate": "1989-12-31", 
        "hometown": gps_vpn_pair["GPSlocation"],
        "workplace": f"{random.choice(['Elite', 'Luxury', 'Premium'])} Hairdressers",
        "jobtitle": random.choice(["Colourist", "Personal Stylist", "Sales Management", "Social Media Marketing", 'Stylist']),
        "college": f"{gps_vpn_pair['VPNlocation']} University",
        "promptAnswer1": random.choice([f"Catch me on instaaa 🌀{ig_handle}",f"check me on da gramm (if u dare tho) 📷{ig_handle}", f"find me on iggg 🫶🏼 {ig_handle}", f"check me on da grammieeee 📸{ig_handle}"]),
        "promptAnswer2": random.choice([
        "midnight adventuressss 👹", "Michael jackson screaming hee-hee",
        "a lot of foot massages, wont even lie haha", "colourssss", "your favourite rollercoaster", "everything except politics",
        "minecraft"]),
        "promptAnswer3": random.choice([
        "otters sleep while holding hands so they dont drift away :)", "my apples are interestingly prominent.",
        "there are more frog species than car brands", "i have proper apples (you interpret that (good luck with it))",
        "we will we will rock you"]) # type: ignore
        } 
    
    iterations_data.append(iteration)

# Appium desired capabilities setup using XCUITestOptions
options = XCUITestOptions()
options.platformName = 'iOS'
options.platformVersion = '16.7.4'
options.deviceName = 'iPhone 8'
options.udid = '16069a525a8afb9c2bebf8f984a3f4d8ac43ebc2'
options.automationName = 'XCUITest'
options.bundleId = 'co.hinge.mobile.ios'
options.xcodeOrgId = 'SHA.com'
options.xcodeSigningId = 'iPhone Developer'

# # Prevent WebDriverAgent from being reset or uninstalled
# options.set_capability('noReset', True)

# Assign port 8101
options.set_capability('wdaLocalPort', 8101)

# Set up the Appium driver with host and port 127.0.0.1:4723
driver = webdriver.Remote('http://127.0.0.1:4723', options=options)

# daisysms API variables
API_KEY = '7giTr2tqnWlmtHV6BpHfJugCZDLtSU'
COUNTRY_CODE = '55'  # country number from daisysms API country list
MAX_PRICE = '0.6'

def create_driver_session():
    driver = webdriver.Remote('http://127.0.0.1:4723', options=options)
    return driver

# Generate random number between a minimum and maximum
def generate_random_number(min_value, max_value):
    return random.randint(min_value, max_value)

# Function to fetch the phone number from Daisy API
def get_phone_number():
    url = 'https://daisysms.com/stubs/handler_api.php'
    data = {
        'api_key': API_KEY,  # Replace with your actual API key
        'action': 'getNumber',
        'service': 'vz',  # 'vz' for Hinge
        'max_price': 0.40  # Adjust max price as needed
    }

    response = requests.get(url, params=data)
    response_data = response.text.split(':')

    # Print the full response for debugging
    print("API Response:", response_data)

    if response_data[0] == 'ACCESS_NUMBER':
        order_id = response_data[1]
        phone_number = response_data[2]

        # Remove leading '1' from the phone number if it starts with '1'
        if phone_number.startswith('1'):
            phone_number = phone_number[1:]

        print(f"Phone number purchased: {phone_number}")
        return phone_number, order_id
    else:
        print("Failed to get phone number:", response_data[0])
        return None, None

# Function to poll for the SMS verification code
def get_verification_code(order_id):
    url = 'https://daisysms.com/stubs/handler_api.php'
    data = {
        'api_key': API_KEY,  # Replace with your actual API key
        'action': 'getStatus',
        'id': order_id
    }

    # Polling until the SMS is received
    for _ in range(10):  # Poll up to 10 times (adjust the number and delay as needed)
        response = requests.get(url, params=data)
        response_data = response.text.split(':')

        # Print the full response for debugging
        print("SMS Check Response:", response_data)

        if response_data[0] == 'STATUS_OK':
            sms_code = response_data[1]
            print(f"Received SMS code: {sms_code}")
            return sms_code
        elif response_data[0] == 'STATUS_WAIT_CODE':
            print("No SMS yet. Retrying in 10 seconds...")
            time.sleep(10)  # Wait for 10 seconds before retrying
        else:
            print(f"Failed to get SMS code: {response_data[0]}")
            break

    print("Failed to receive SMS.")
    # Optionally handle retries using Appium code here for resending the code
    return None

def enter_sms_code(driver, sms_code):
    # Ensure the code is 6 digits long
    if len(sms_code) != 6:
        print(f"Invalid SMS code length: {sms_code}")
        return

    # Enter each digit in the corresponding text field
    try:
        # First digit
        el1 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[1]/XCUIElementTypeTextField")
        el1.clear()
        el1.send_keys(sms_code[0])
        
        # Second digit
        el2 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeTextField")
        el2.clear()
        el2.send_keys(sms_code[1])
        
        # Third digit
        el3 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeTextField")
        el3.clear()
        el3.send_keys(sms_code[2])
        
        # Fourth digit
        el4 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeTextField")
        el4.clear()
        el4.send_keys(sms_code[3])
        
        # Fifth digit
        el5 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeTextField")
        el5.clear()
        el5.send_keys(sms_code[4])
        
        # Sixth digit
        el6 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther[6]/XCUIElementTypeTextField")
        el6.clear()
        el6.send_keys(sms_code[5])

        print(f"SMS code {sms_code} entered successfully!")
        
    except Exception as e:
        print(f"Error during SMS code entry: {e}")


# Perform touch action using W3C actions
def perform_touch_action(driver, x, y, hold_duration=0.1):
    try:
        print(f"Performing touch action at ({x}, {y}) with hold duration of {hold_duration} seconds.")
        touch = PointerInput(POINTER_TOUCH, "touch")
        actions = ActionChains(driver)
        actions.w3c_actions.pointer_action.move_to_location(x, y)
        actions.w3c_actions.pointer_action.pointer_down()
        actions.w3c_actions.pointer_action.pause(hold_duration)
        actions.w3c_actions.pointer_action.pointer_up()
        actions.perform()
        print(f"Touch action completed at ({x}, {y}).")
    except Exception as e:
        print(f"Error during touch action: {e}")

# Tap and hold at a specific location
def tap_and_hold(driver, x, y, hold_duration=2):
    try:
        touch = PointerInput(POINTER_TOUCH, "touch")
        actions = ActionChains(driver)
        print(f"Tapping and holding at ({x}, {y}) for {hold_duration} seconds.")
        actions.w3c_actions.pointer_action.move_to_location(x, y)
        actions.w3c_actions.pointer_action.pointer_down()
        actions.w3c_actions.pointer_action.pause(hold_duration)
        actions.w3c_actions.pointer_action.pointer_up()
        actions.perform()
        print(f"Hold and release completed at ({x}, {y}).")
    except Exception as e:
        print(f"Error during tap and hold: {e}")

def random_date(min_date_str, max_date_str):
    # Convert strings to datetime objects
    min_date = datetime.strptime(min_date_str, "%Y-%m-%d")
    max_date = datetime.strptime(max_date_str, "%Y-%m-%d")

    # Generate a random number of days between the two dates
    delta = max_date - min_date
    random_days = random.randint(0, delta.days)

    # Add the random number of days to the minimum date and return it formatted as DD MM YYYY
    return (min_date + timedelta(days=random_days)).strftime("%m %d %Y")

def extract_verification_code_from_subject_and_body(subject, body):
    # Example function to extract the verification code (assuming it's a 6-digit code)
    import re
    # Try to extract from subject first
    match = re.search(r'\d{6}', subject)
    if match:
        return match.group(0)
    
    # If not found in subject, try the body
    match = re.search(r'\d{6}', body)
    if match:
        return match.group(0)
    
    return None

def get_verification_code_from_email(emailAddress, emailPassword, max_attempts=1, poll_delay=10):
    for attempt in range(max_attempts):  # Poll up to max_attempts times
        try:
            print(f"Connecting to IMAP server for {emailAddress}...")
            addy = imaplib.IMAP4_SSL("imap.gmx.com", 993)  # for GMX 

            print(f"Logging in as {emailAddress}...")
            addy.login(emailAddress, emailPassword)
            print(f"Successfully logged in as {emailAddress}.")

            print(f"Selecting the inbox for {emailAddress}...")
            addy.select("inbox")

            print(f"Searching for emails with 'Verification' in the subject for {emailAddress}...")
            result, data = addy.search(None, '(SUBJECT "Verification")')

            if result != 'OK':
                print(f"Search failed with result: {result}")
                return None

            email_ids = data[0].split()

            if not email_ids:
                print(f"No verification emails found for {emailAddress}. Retrying in {poll_delay} seconds...")
                time.sleep(poll_delay)
                continue

            print(f"Found {len(email_ids)} email(s) for {emailAddress}. Fetching the latest one...")

            latest_email_id = email_ids[-1]
            result, msg_data = addy.fetch(latest_email_id, "(RFC822)")

            if result != 'OK':
                print(f"Failed to fetch email with result: {result}")
                return None

            for response_part in msg_data:
                if isinstance(response_part, tuple):
                    msg = email.message_from_bytes(response_part[1])

                    email_subject = decode_header(msg["Subject"])[0][0]
                    if isinstance(email_subject, bytes):
                        email_subject = email_subject.decode()

                    print(f"Email subject: {email_subject}")

                    if msg.is_multipart():
                        for part in msg.walk():
                            if part.get_content_type() == "text/plain":
                                email_body = part.get_payload(decode=True).decode('utf-8', errors='replace')
                                print(f"Email body (text/plain): {email_body}")
                                break
                    else:
                        email_body = msg.get_payload(decode=True).decode('utf-8', errors='replace')
                        print(f"Email body: {email_body}")

                    verification_code = extract_verification_code_from_subject_and_body(email_subject, email_body)
                    if verification_code:
                        print(f"Verification code found for {emailAddress}: {verification_code}")
                        return verification_code
                    else:
                        print(f"No verification code found in the email for {emailAddress}. Retrying in {poll_delay} seconds...")
                        time.sleep(poll_delay)
                        continue

        except Exception as e:
            print(f"Failed to retrieve email for {emailAddress}. Error: {e}")
            time.sleep(poll_delay)
            continue

        finally:
            print(f"Logging out of email for {emailAddress}.")
            addy.logout()

    print(f"Failed to receive verification code after {max_attempts} attempts.")
    return None

def enter_email_verification_code(driver, email_verification_code):
    try:
        # Ensure the email verification code is exactly 6 digits long
        if len(email_verification_code) != 6:
            print("Invalid email verification code length. Must be 6 digits.")
            return

        # Define both potential element path templates
        primary_element_paths = [
            # First digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[1]/XCUIElementTypeTextField",
            # Second digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[2]/XCUIElementTypeTextField",
            # Third digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[3]/XCUIElementTypeTextField",
            # Fourth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[4]/XCUIElementTypeTextField",
            # Fifth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[5]/XCUIElementTypeTextField",
            # Sixth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[6]/XCUIElementTypeTextField"
        ]

        alternative_element_paths = [
            # First digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[1]/XCUIElementTypeTextField",
            # Second digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[2]/XCUIElementTypeTextField",
            # Third digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[3]/XCUIElementTypeTextField",
            # Fourth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[4]/XCUIElementTypeTextField",
            # Fifth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[5]/XCUIElementTypeTextField",
            # Sixth digit
            "**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeOther[6]/XCUIElementTypeTextField"
        ]

        # Function to attempt entering the code using given element paths
        def enter_code(paths):
            for index, path in enumerate(paths):
                el = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=path)
                el.clear()
                el.send_keys(email_verification_code[index])
                print(f"Entered digit {email_verification_code[index]} in field {index + 1}")

        # First attempt with primary paths
        try:
            enter_code(primary_element_paths)
            print("Email verification code entered successfully using primary paths!")
        except Exception as primary_error:
            print(f"Primary paths failed: {primary_error}")

            # Try alternative paths if primary paths failed
            try:
                enter_code(alternative_element_paths)
                print("Email verification code entered successfully using alternative paths!")
            except Exception as alternative_error:
                print(f"Both primary and alternative paths failed: {alternative_error}")
    
    except Exception as e:
        print(f"Error entering email verification code: {e}")

def retrieve_and_enter_verification_code(driver, email_address, email_password, max_attempts=5, delay_between_attempts=10):
    attempts = 0
    code_resent = False  # Track if the code has been resent

    while attempts < max_attempts:
        # Try to get the verification code from the email
        print(f"Attempt {attempts + 1}/{max_attempts}: Checking for verification code...")
        verification_code = get_verification_code_from_email(email_address, email_password, max_attempts=5)

        if verification_code:
            print(f"Final Verification Code for {email_address}: {verification_code}")
            enter_email_verification_code(driver, verification_code)

            # Click the 'Next' button to proceed
            try:
                print("Clicking the Next button...")
                next_button = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
                next_button.click()

                # Wait for 15 seconds after clicking 'Next' for the Date Input Field to appear
                print("Waiting for 15 seconds to check for the Date Input Field...")
                time.sleep(15)

                # Try to find the 'Date Input Field'
                date_input_field = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Date Input Field"`]')
                print("Date Input Field found. Proceeding...")
                return True  # Exit the function after successful verification and transition

            except Exception as e:
                print(f"Date Input Field not found or couldn't click Next. Restarting the app. Error: {e}")

                # Close the app
                driver.terminate_app("co.hinge.mobile.ios")  # Replace with your app's bundle ID
                print("App closed.")

                # Reopen the app
                driver.activate_app("co.hinge.mobile.ios")  # Replace with your app's bundle ID
                print("App reopened.")

                # Re-enter the verification code after reopening the app
                time.sleep(3)  # Give time for the app to reopen
                print("Re-entering the verification code...")
                enter_email_verification_code(driver, verification_code)  # Re-enter the code

        else:
            print(f"Failed to retrieve verification code for {email_address}, attempt {attempts+1}/{max_attempts}")

        # Wait before attempting again
        time.sleep(delay_between_attempts)
        attempts += 1

        # If attempts exceed max_attempts and code hasn't been resent, trigger the resend code logic
        if attempts >= max_attempts and not code_resent:
            print("Attempting to resend verification code after 3 failed attempts.")
            try:
                trouble_verifying = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Having trouble verifying?"`]')
                trouble_verifying.click()
                print("Clicked on 'Having trouble verifying?'")

                time.sleep(2.7)

                resend_code = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send code again"`]')
                resend_code.click()
                print("Clicked on 'Send code again'")

                print("Verification code resent.")
                code_resent = True  # Mark that the code has been resent

                # Reset attempts to restart the process after resending the code
                attempts = 0
                print("Restarting verification code checking process after resending code.")

            except Exception as e:
                print(f"Couldn't resend the code: {e}")
                return False

    print(f"Verification code not retrieved after {max_attempts} attempts.")
    return False

def click_next_and_reopen_if_needed(driver, verification_code):
    # Try clicking "Next" button
    try:
        el39 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el39.click()
        time.sleep(4.7)  # Wait to see if the next screen loads

        # Check if the expected element on the next screen is present
        next_screen_element = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Date Input Field"`]')
        if next_screen_element:
            print("Next screen loaded successfully.")
            return True

    except Exception as e:
        print("Failed to find or click 'Next' button or load the next screen:", e)

    # If we reach here, it means "Next" didn't work, so fully close and reopen the app
    print("Closing and reopening the app due to failure to advance...")

    # Fully close and relaunch the app
    driver.close_app()
    time.sleep(2)  # Small delay to ensure the app fully closes
    driver.launch_app()
    time.sleep(5)  # Wait for app to relaunch fully

    # Resend the code without fetching again (using the already fetched `verification_code`)
    try:
        print(f"Resending verification code: {verification_code}")
        emailfield = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "<EMAIL>"`]')
        emailfield.send_keys(emailAddress)
        time.sleep(0.77)
        emailnext = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        emailnext.click()
        time.sleep(2.8)
        enter_email_verification_code(driver, verification_code)
        time.sleep(2)  # Give time for verification code to be processed
    except Exception as e:
        print("Failed to re-enter the verification code after reopening the app:", e)

    return False

def tap_random_times(driver, x, y, hold_duration=0.2):
    # Randomly choose between 1, 2, or 3 taps
    tap_count = random.choice([1, 2, 3])
    print(f"Tapping and holding {tap_count} time(s).")
    
    # Perform the tap and hold action the chosen number of times
    for _ in range(tap_count):
        tap_and_hold(driver, x, y, hold_duration)

def perform_randomized_image_selection(driver):
    # Define the tap actions (coordinates for selecting images) in a list
    image_selection_actions = [
        (72, 175, 0.2),
        (194, 182, 0.2),
        (335, 173, 0.2),
        (66, 304, 0.2),
        (208, 299, 0.2),
        (313, 306, 0.2)
    ]

    # Shuffle the actions to randomize the order
    random.shuffle(image_selection_actions)

    # Perform each tap action in the randomized order
    for x, y, hold_duration in image_selection_actions:
        tap_and_hold(driver, x, y, hold_duration)

def perform_randomized_image_selection4deletion(driver):
    # Define the tap actions (coordinates for selecting images) in a list
    image_selection_actions = [
        (61, 429, 0.2),
        (188, 436, 0.2),
        (328, 419, 0.2),
        (60, 530, 0.2),
        (189, 520, 0.2),
        (313, 538, 0.2)
    ]

    # Shuffle the actions to randomize the order
    random.shuffle(image_selection_actions)

    # Perform each tap action in the randomized order
    for x, y, hold_duration in image_selection_actions:
        tap_and_hold(driver, x, y, hold_duration)

def find_correct_sliders(driver):
    # Loop through possible values for both minimum and maximum between 18 and 38
    for min_value in range(18, 39):  # Values from 18 to 38
        for max_value in range(18, 39):
            try:
                # Try finding the minimum slider
                el_min = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeSlider[`value == "minimum: {min_value}"`]')
                print(f"Found the minimum slider with value: {min_value}")
                
                # Try finding the maximum slider
                el_max = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value=f'**/XCUIElementTypeSlider[`value == "maximum: {max_value}"`]')
                print(f"Found the maximum slider with value: {max_value}")

                # If both are found, return them
                return el_min, el_max

            except Exception as e:
                # If it doesn't find the slider, continue searching
                print(f"Combination {min_value} and {max_value} did not match. Trying next...")

    # If no matching sliders are found, raise an error
    raise Exception("No valid sliders found in the range 18 to 38.")

def scroll_down(driver):

    # Calculate swipe start and end points (middle of the screen horizontally)
    start_x = 334
    start_y = 462  
    end_y = 302    # End near the top

    # Perform the scroll using 'mobile: swipe' for iOS
    driver.execute_script('mobile: swipe', {
        'direction': 'up',
        'startX': start_x,
        'startY': start_y,
        'endX': start_x,
        'endY': end_y,
        'duration': 800  # Duration in milliseconds
    })


def scroll_and_find_element(driver, element_locator, max_scrolls=10):
    scroll_attempts = 0

    while scroll_attempts < max_scrolls:
        try:
            # Try to find the element using its locator
            element = driver.find_element(AppiumBy.IOS_CLASS_CHAIN, element_locator)
            time.sleep(1.7)
            # If found, click the element
            element.click()
            print("Element found and clicked.")
            return True

        except NoSuchElementException:
            # If the element is not found, scroll down
            print(f"Element not found. Scrolling down... Attempt {scroll_attempts + 1}/{max_scrolls}")
            scroll_down(driver)
            scroll_attempts += 1

    print("Element not found after maximum scroll attempts.")
    return False

# THE PROCESS
def main_process(driver, variables):
    try:
        # Extract variables from the dictionary
        containerName = variables["containerName"]
        GPSlocation = variables["GPSlocation"]
        VPNlocation = variables["VPNlocation"]
        girlName = variables["girlName"]
        emailAddress = variables["emailAddress"]
        emailPassword = variables["emailPassword"]
        minimumDate = variables["minimumDate"]
        maximumDate = variables["maximumDate"]
        hometown = variables["hometown"]
        workplace = variables["workplace"]
        jobtitle = variables["jobtitle"]
        college = variables["college"]
        promptAnswer1 = variables["promptAnswer1"]
        promptAnswer2 = variables["promptAnswer2"]
        promptAnswer3 = variables["promptAnswer3"]

        # driver.execute_script('mobile:pressButton', {"name": "home"})        
        # time.sleep(2)

        # # # The URL that triggers the proxy rotation
        # # rotation_link = 'https://elusiveproxy.com/proxy-rotate/20/285/d2341c43a8797c1c778d4cbdac8a6102'

        # # # Make a simple GET request to the link
        # # response = requests.get(rotation_link)

        # # # Print the status to confirm the call was successful
        # # print(f"Proxy rotation triggered, status code: {response.status_code}")

        # ## IN CASE OF IMAGE SPOOFING
        # # Open Photos and delete latest 6 images
        # el97 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Photos"`]')
        # el97.click()

        # driver.terminate_app('com.apple.mobileslideshow')
        # driver.activate_app('com.apple.mobileslideshow')

        # time.sleep(1.78)
        # try:
        #     el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Albums"`]')
        #     el98.click()
        #     time.sleep(2.78)
        # except Exception as e:
        #     pass

        # try:
        #     el98 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Recents"`]')
        #     el98.click()
        # except Exception as e:
        #     pass

        # time.sleep(1.78)
        # try:
        #     el99 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Select"`]')
        #     el99.click()
        #     if not el99:
        #         print("gay shit wuth pic deletion")
        #         return
        # except Exception as e:
        #     el99 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Select"`]')
        #     el99.click()
        #     if not el99:
        #         print("gay shit wuth pic deletion")
        #         return

        # time.sleep(2.78)
        # perform_randomized_image_selection4deletion(driver)
        # time.sleep(1.7)
        # el100 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Delete"`]')
        # el100.click()
        # time.sleep(3.78)
        # el101 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Delete 6 Photos"`]')
        # el101.click()
        # time.sleep(2.77)
        # driver.execute_script('mobile:pressButton', {"name": "home"})

        # time.sleep(2.77)

        # driver.execute_script('mobile:pressButton', {"name": "home"})
        # time.sleep(1)

        # # Tap and hold on the Hinge app (put standardly at right bottom pinned) and create new crane container
        # tap_and_hold(driver, 319, 623, hold_duration=1.3)
        # time.sleep(1)
        # tap_and_hold(driver, 280, 525, hold_duration=0.2)
        # time.sleep(2)
        # scroll_and_find_element(driver, '**/XCUIElementTypeButton[`name == "New Container"`]')
        # time.sleep(3)
        # # newcont = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="'**/XCUIElementTypeButton[`name == 'New Container'`]'")
        # # newcont.click()
        # time.sleep(3)

        # # Checking for name field, filling in new container name and creating the container
        # try:
        #     print("Filling in the container name.")
            
        #     # Send input to a text field
        #     el2 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeTextField")
        #     el2.clear()
        #     el2.send_keys(containerName)  #Container Name
            
        #     # Click "Next" button
        #     el3 = driver.find_element(by=AppiumBy.ACCESSIBILITY_ID, value="Create")
        #     el3.click()

        #     print("Finalized container creation.")

        # except Exception as e:
        #     print(f"Error during interaction: {e}")

        # # Go home to be sure, open GPS Master
        # driver.execute_script('mobile:pressButton', {"name": "home"})
        # time.sleep(2)
        # el4 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "GPS Master"`]')
        # el4.click()

        # # set location
        # time.sleep(3)
        # el5 = driver.find_element(by=AppiumBy.ACCESSIBILITY_ID, value="Fake GPS")
        # el5.click()
        # time.sleep(1.7)
        # el6 = driver.find_element(by=AppiumBy.ACCESSIBILITY_ID, value="Search")
        # el6.click()
        # time.sleep(1.7)
        # el7 = driver.find_element(by=AppiumBy.ACCESSIBILITY_ID, value="Search Maps")
        # el7.click()
        # time.sleep(1.7)
        # el10 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeSearchField[`name == "Search Maps"`]')
        # el10.clear()
        # el10.send_keys(GPSlocation)
        # time.sleep(4.7)
        # el1 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeTable/XCUIElementTypeCell/XCUIElementTypeOther[2]/XCUIElementTypeOther')
        # el10.click()
        # time.sleep(2)

        # el9 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeTable/XCUIElementTypeCell/XCUIElementTypeOther[2]/XCUIElementTypeOther')
        # el9.click()

        # time.sleep(2)

        # # back to home
        # driver.execute_script('mobile:pressButton', {"name": "home"})

        # time.sleep(1)

        # # open Mullvad to rotate IP
        # el12 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Mullvad VPN"`]')
        # el12.click()

        # time.sleep(2)

        # # Changing IP location
        # try:
        #     el13 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "selectLocationButton"`]')
        #     el13.click()

        #     time.sleep(1)

        #     print("Filling in the Mullvad location.")
            
        #     # Send location to text field
        #     tap_and_hold(driver, 96, 125, hold_duration=0.2)
        #     el14 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Entry"`]')
        #     el14.clear()
        #     el14.send_keys(VPNlocation)  # Location Name

        #     # Select location
        #     el15 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeCell[`name == "cityLocationCell"`]/XCUIElementTypeOther[1]')
        #     el15.click()

        #     time.sleep(1)

        # except Exception as e:
        #     print(f"Error during interaction: {e}")

        # # back to home
        # driver.execute_script('mobile:pressButton', {"name": "home"})

        # time.sleep(4)

        # # Open Hinge app
        # el16 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Hinge"`]')
        # el16.click()

        # time.sleep(2.7)

        # # Step 2: Tap "Create account" button
        # el17 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Create account"`]')
        # el17.click()

        # time.sleep(2)
        
        # # countryflag = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "+44"`]')
        # # countryflag.click()
        
        # # scroll_and_find_element(driver, '**/XCUIElementTypeStaticText[`name == "United States"`]')
        
        # # targetcountry = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "United States"`]')
        # # targetcountry.click()

        # try:
        #     # Step 1: Fetch phone number and order ID from daisysms API
        #     phone_number, orderid = get_phone_number()
        #     if phone_number is None or orderid is None:
        #         print("Could not fetch phone number. Exiting process.")
        #         return

        #     time.sleep(5.7)

        #     # Step 3: Fill the phone number fetched from the API
        #     el18 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value="**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView[2]/XCUIElementTypeOther/XCUIElementTypeOther[1]/XCUIElementTypeOther/XCUIElementTypeOther[1]/XCUIElementTypeTextField")
        #     el18.clear()
        #     el18.send_keys(phone_number)
        #     print(f"Phone number {phone_number} entered successfully!")
            
        #     try:
        #         el19 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        #         el19.click()
        #         print("Next button clicked successfully!")
        #     except Exception as e:
        #         print(f"Failed to click the Next button. Error: {e}")
        #         print("Terminating the process due to too many verification requests.")
        #         return  # Terminate the process

        #     # Step 4: Poll for the SMS code from daisysms API
        #     sms_code = get_verification_code(orderid)
        #     if sms_code is None:
        #         print("Could not fetch SMS code. Exiting process.")
        #         return

        # except Exception as e:
        #     print(f"Error during process: {e}")

        # # Step 5: Enter the SMS code in 6 separate fields
        # enter_sms_code(driver, sms_code)

        # time.sleep(1)
        # el19 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        # el19.click()

        # time.sleep(7.7)

        # # Clicking next to enter basic info
        # time.sleep(7.7)
        # el20 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Enter basic info"`]')
        # el20.click()

        # time.sleep(4.7)

        # el21 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "First name (required)"`]')
        # el21.send_keys(girlName) 

        # time.sleep(3.2)

        # el22 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        # el22.click()

        # time.sleep(3.4)

        # # filling in the email account
        # el23 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "<EMAIL>"`]')
        # el23.send_keys(emailAddress)

        # time.sleep(0.7)

        # el24 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        # el24.click()

        # time.sleep(2.7)

        # #### LOGIN WITH OTHER EMAIL WORKFLOW

        # # Clicking "No Thanks" instead of logging in somewhere
        # el25 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "No thanks"`]')
        # el25.click()

        # # Wait a while before fetching and entering verification code
        # time.sleep(7.7)

        # driver.background_app(2)

        verification_code = retrieve_and_enter_verification_code(driver, emailAddress, emailPassword)

        if verification_code:
            time.sleep(4.7)
            click_next_and_reopen_if_needed(driver, verification_code)
            time.sleep(3.7)
        else:
            print("Verification code not retrieved.")

        # Fill in randomized date, using minimum date and maximum date per model
        el40 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "Date Input Field"`]')
    
        el40.send_keys(random_date(minimumDate, maximumDate))

        el41 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el41.click()

        time.sleep(1.77)

        el42 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Confirm"`]')
        el42.click()

        time.sleep(7.7)

        # [IMPERFECT] tapping enable notifications and going next
        tap_and_hold(driver, 133, 245, hold_duration=0.2)

        el43 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el43.click()

        # Clicking "Add more details"
        time.sleep(4.7)
        el44 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Add more details"`]')
        el44.click()

        # Clicking "Go to current location" to ensure GPS location is taken and then next
        time.sleep(4.7)
        el45 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Go to current location"`]')
        el45.click()
        time.sleep(4.7)
        el46 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el46.click()

        # Skipping gay ahh pronouns
        time.sleep(4.7)
        el47 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el47.click()

        # Selecting Gender and next
        time.sleep(4.7)
        el48 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Woman"`]')
        el48.click()
        el49 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el49.click()

        # Selecting sexuality and next
        time.sleep(4.7)
        el50 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Straight"`]')
        el50.click()
        el51 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el51.click()

        # Selecting who you want to date and next
        time.sleep(4.7)
        el52 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Men"`]')
        el52.click()
        el53 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el53.click()

        # Selecting relationship type and next
        time.sleep(3.7)
        el54 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Monogamy"`]')
        el54.click()
        el55 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el55.click()

        # Selecting dating intention and next
        time.sleep(2.7)
        el56 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Figuring out my dating goals"`]')
        el56.click()
        el57 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el57.click()

        # Randomly change height and next
        time.sleep(2.7)
        tap_random_times(driver, 186, 370, hold_duration=0.2)
        el58 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el58.click()

        # Change ethnicity (not visible)
        time.sleep(2.7)
        el59 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeOther[`name == "checked; Visible on profile"`][6]')
        el59.click()
        tap_and_hold(driver, 337, 263, hold_duration=0.2)
        el60 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el60.click()

        # Do you have children?
        time.sleep(2.7)
        tap_and_hold(driver, 337, 321, hold_duration=0.2)
        el61 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el61.click()

        # Family plans
        time.sleep(2.7)
        tap_and_hold(driver, 337, 359, hold_duration=0.2)
        el62 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el62.click()

        # # # Hometown
        # # time.sleep(2.7)
        # # el63 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "Home town"`]')
        # # el63.send_keys(hometown)
        el64 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el64.click()

        # Workplace
        time.sleep(2.7)
        el65 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "Workplace"`]')
        el65.send_keys(workplace)
        el66 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el66.click()

        # Job title
        time.sleep(2.7)
        el67 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "Job title"`]')
        el67.send_keys(jobtitle)
        el68 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el68.click()

        # # College or univeºrsity
        # time.sleep(2.7)
        # el69 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTextField[`value == "Add a college or university"`]')
        # el69.send_keys(college)
        el70 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el70.click()

        # Degree
        time.sleep(2.7)
        tap_and_hold(driver, 337, 303, hold_duration=0.2)
        el71 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el71.click()

        # Religious beliefs
        time.sleep(2.7)
        tap_and_hold(driver, 337, 471, hold_duration=0.2)
        el72 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el72.click()

        # Political beliefs
        time.sleep(2.7)
        tap_and_hold(driver, 337, 471, hold_duration=0.2)
        el73 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el73.click()

        # Do you drink?
        time.sleep(2.7)
        tap_and_hold(driver, 337, 262, hold_duration=0.2)
        el74 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el74.click()

        # Do you smoke tobacco?
        time.sleep(2.7)
        tap_and_hold(driver, 337, 320, hold_duration=0.2)
        el75 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el75.click()

        # Do you smoke weed?
        time.sleep(2.7)
        tap_and_hold(driver, 337, 320, hold_duration=0.2)
        el76 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el76.click()

        # Do you use drugs?
        time.sleep(2.7)
        tap_and_hold(driver, 337, 320, hold_duration=0.2)
        el77 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el77.click()
        time.sleep(3.7)

        # check for "We value your privacy" screen
        try:
            el877 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "I accept"`]')
            el877.click()
            el878 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
            el878.click()

        except Exception as e:
            print(f"No 'We value your privacy' screen")
            pass

        # Fill out your profile
        time.sleep(3.7)
        tap_and_hold(driver, 337, 320, hold_duration=0.2)
        el78 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Fill out your profile"`]')
        el78.click()

        # Add pictures
        time.sleep(1.7)
        el78 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeImage[`name == "mediaInputAddMedia4"`][1]')
        el78.click()
        time.sleep(1.77)
        el79 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Camera roll"`]')
        el79.click()

        # Photos screen open, taps the first six images in a random order
        time.sleep(17.5)
        perform_randomized_image_selection(driver)

        time.sleep(9.7)

        # click "Add"
        el80 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Add"`]')
        el80.click()

        # click next on all the pics, then done, and next on the hinge interface
        time.sleep(8.7)
        for i in range(5):
            el81 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
            el81.click()
            time.sleep(1.7)
        el82 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]')
        el82.click()
        time.sleep(3.3)
        el83 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el83.click()

        time.sleep(23.7)
        try:
            # Try to find the button element with the given name
            print("Checking for the prompt screen...")
            missing_prompt_button = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Missing prompt. Select a Prompt and write your own answer."`][1]')
            print("'Prompt screen found, continuing with the process...")
            # Continue the rest of your method here
            pass  # This allows the function to continue

        except Exception as e:
            # If the element is not found, wait and retry or move on
            print(f"'Missing prompt' button not found. Waiting and retrying. Error: {e}")
            time.sleep(10)  # Wait for 4 seconds or as necessary

        # Prompts
        # Prompt no. 1
        el84 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Missing prompt. Select a Prompt and write your own answer."`][1]')
        el84.click()
        time.sleep(2.7)

        # click View All
        el777 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "View all"`]')
        el777.click()

        time.sleep(2.7)

        el778 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "All I ask is that you"`]')
        el778.click()
        time.sleep(1.7)
        tap_and_hold(driver, 149, 223, 0.2) # click the textfield for the prompt answer
        
        time.sleep(1.7)
        el85 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Be completely true to yourself."`]')
        el85.send_keys(promptAnswer1)
        time.sleep(1.7)
        el86 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]')
        el86.click()

        # Prompt no. 2
        time.sleep(1.7)
        el87 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Missing prompt. Select a Prompt and write your own answer."`][2]')
        el87.click()
        time.sleep(1.7)

        # click View All
        el777 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "View all"`]')
        el777.click()

        el779 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Dating me is like"`]')
        el779.click()
        tap_and_hold(driver, 149, 223, 0.2) # click the textfield for the prompt answer
        time.sleep(1.7)
        el88 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Seeing a D-list celebrity at the airport. Mildly thrilling."`]')
        el88.send_keys(promptAnswer2)
        time.sleep(1.7)
        el89 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]')
        el89.click()

        # Prompt no. 3
        time.sleep(1.7)
        el90 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Missing prompt. Select a Prompt and write your own answer."`]')
        el90.click()
        time.sleep(1.7)

        el777 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "View all"`]')
        el777.click()

        el780 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "A random fact I love is"`]')
        el780.click()
        tap_and_hold(driver, 149, 223, 0.2) # click the textfield for the prompt answer
        time.sleep(2.7)
        el91 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "It\'s illegal to own just one guinea pig in Switzerland because they get lonely."`]')
        el91.send_keys(promptAnswer3)
        time.sleep(2.7)
        el92 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Done"`]')
        el92.click()
        time.sleep(3.7)

        el93 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el93.click()
        time.sleep(3.7)

        el94 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Next"`]')
        el94.click()
        time.sleep(2.7)

        # skip the upsell screen and the "All Done" screen
        el95 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Maybe later"`]')
        el95.click()
        time.sleep(1.7)
        el96 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Start sending likes"`]')
        el96.click()

        # Let it loaadd
        time.sleep(17.7)
        tap_and_hold(driver, 222, 222, 0.2)

        el102 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeTabBar[`name == "Tab Bar"`]/XCUIElementTypeButton[5]')
        el102.click()

        time.sleep(3.7)

        el103 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "dials medium"`]')
        el103.click()

        time.sleep(1.7)

        el104 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Maximum distance"`]')
        el104.click()

        time.sleep(1.7)

        el105 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeSlider[`value == "160 km"`]')
        el105.send_keys(random.randint(0.15,0.6))

        time.sleep(2.7)
        
        # go back
        tap_and_hold(driver, 20, 42, 0.14)

        time.sleep(2.7)

        el1055 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "close"`]')
        el1055.click()

        time.sleep(2.7)

        el106 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeImage[`name == "tabBarDiscoverDeselected"`]')
        el106.click()

        time.sleep(1.7)

        el1065 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "OK, got it"`]')
        el1065.click()

        time.sleep(1.7)

        el1066 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Not now"`]')
        el1066.click()

        time.sleep(1.7)

        el107 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Skip"`]')
        el107.click()

        time.sleep(1.7)

        el110 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Like"`][1]')
        el110.click()

        time.sleep(1.7)

        el108 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send Like"`]')
        el108.click()

        time.sleep(1.7)

        el1085 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send Like anyway"`]')
        el1085.click()

        try:
            el109 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "close"`]')
            el109.click()
        except Exception as e:
            pass

        el110 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Like"`][1]')
        el110.click()

        time.sleep(1.7)

        el108 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send Like"`]')
        el108.click()

        time.sleep(1.7)

        el107 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Skip"`]')
        el107.click()

        time.sleep(1.7)

        el110 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Like"`][1]')
        el110.click()

        time.sleep(1.7)

        el108 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send Like"`]')
        el108.click()

        time.sleep(1.7)

        el107 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Skip"`]')
        el107.click()

        time.sleep(1.7)

        el110 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Like"`][1]')
        el110.click()

        time.sleep(3.3)

        el108 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeStaticText[`name == "Send Like"`]')
        el108.click()

        time.sleep(1.7)

        el107 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Skip"`]')
        el107.click()

        time.sleep(1.7)

        el107 = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeButton[`name == "Skip"`]')
        el107.click()

        time.sleep(1.7)

        driver.execute_script('mobile:pressButton', {"name": "home"})
        time.sleep(4.7)

    except Exception as e:
        print(f"Error during process for {containerName}: {e}")


# Loop through the iterations_data list and call the main_process with each set of variables
for i, variables in enumerate(iterations_data):
    print(f"Starting process for iteration {i + 1}")
    main_process(driver, variables)
    print(f"Completed process for iteration {i + 1}")

# End the session
driver.quit()
