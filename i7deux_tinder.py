from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
from appium.options.ios import XCUITestOptions
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hains
from selenium.webdriver.common.actions.interaction import POINTER_TOUCH
from selenium.webdriver.common.actions.pointer_input import PointerInput
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.common.exceptions import NoSuchElementException
from datetime import datetime, timedelta
from email.header import decode_header
import random
import imaplib
import email
import re
import os
import base64
import re
import requests
import time

# Appium desired capabilities setup using XCUITestOptions
options = XCUITestOptions()
options.platformName = 'iOS'
options.platformVersion = '15.7.7'
options.deviceName = 'iPhone 7'
options.udid = 'e90dc0f488e51bbabe9806d636e804e8dccb79bc'
options.automationName = 'XCUITest'
options.bundleId = 'com.cardify.tinder'
options.xcodeOrgId = 'SHA.com'
options.xcodeSigningId = 'iPhone Developer'
options.set_capability('newCommandTimeout', 6000)
options.set_capability('wdaConnectionTimeout', 30000)  

# Prevent WebDriverAgent from being reset or uninstalled
options.set_capability('noReset', True)

options.set_capability('useNewWDA', False)
options.set_capability('usePrebuiltWDA', True)

# Assign port 8100
options.set_capability('wdaLocalPort', 8103)

# Set up the Appium driver with host and port 127.0.0.1:4729
driver = webdriver.Remote('http://127.0.0.1:4729', options=options)

# Placeholder lists for data
emails = []  # Fill with email data later
GPS_VPN_pairs = []  # Fill with GPS and VPN data later
girl_names = []  # Fill with girl names later
ig_handle = ""  # Instagram handle placeholder

# Function to generate a container name
def generate_container_name(index, location):
    return f"Container_{index}_{location}"

# Function to define the main process (currently placeholder)
def main_process(driver, variables):

    # Driver setup (fill in your WebDriver options accordingly)
    driver = webdriver.Remote('http://127.0.0.1:4729', options=options)

    # Go home to the first page containing 24 clones
    driver.execute_script('mobile:pressButton', {"name": "home"})

    # Change the proxy IP
    geranium = driver.find_element(by=AppiumBy.IOS_CLASS_CHAIN, value='**/XCUIElementTypeIcon[`name == "Geranium"`]')
    geranium.click()

    # Change the location based on coördinates

# Clone names for iteration
clone_names = [f"Clone{i + 1}" for i in range(24)]  # 24 clones on one page

# List to be filled with iterations data
iterations_data = []

# Loop through the clones and process
for clone in clone_names:
    print(f"Starting process for {clone}")

    # Dynamically generate iteration for the current clone (replace this with the actual data per clone)
    for i in range(len(emails)):
        mail = emails[i]
        gps_vpn_pair = GPS_VPN_pairs[i]
        girl_name = random.choice(girl_names)
        
        iteration = {
            "containerName": f"{clone}_{generate_container_name(i + 1, gps_vpn_pair['GPSlocation'])}",
            "GPSlocation": gps_vpn_pair["GPSlocation"],
            "VPNlocation": gps_vpn_pair["VPNlocation"],
            "girlName": girl_name,
            "emailAddress": mail["emailAddress"],
            "emailPassword": mail["emailPassword"],
            "minimumDate": "1975-01-01",  
            "maximumDate": "1989-12-31", 
            "hometown": gps_vpn_pair["GPSlocation"],
            "workplace": f"{random.choice(['Elite', 'Luxury', 'Premium'])} Yoga Studio",
            "jobtitle": random.choice(["Game Developer", "Personal Trainer", "Fitness Coach, Sales Management, Social Media Marketing"]),
            "college": f"{gps_vpn_pair['VPNlocation']} University",
            "promptAnswer1": random.choice([f"Catch me on instaaa {ig_handle}",f"check me on da gramm (if u dare tho) @{ig_handle}", f"find me on iggg 🫶🏼 {ig_handle}", f"check me on da grammieeee 📸{ig_handle}"]),
            "promptAnswer2": random.choice([
            "midnight adventuressss 👹", 
            "a lot of hair, wont even lie haha", "colourssss", "your favourite rollercoaster",
            "minecraft"]),
            "promptAnswer3": random.choice([
            "otters sleep while holding hands so they dont drift away :)", "my apples are interestingly prominent.",
            "there are more frog species than car brands", "i have proper apples (you interpret that (good luck with it))",
            "the angry German guy is still alive"])
        }

        iterations_data.append(iteration)

    # Loop through the iterations_data list and call the main_process with each set of variables
    for i, variables in enumerate(iterations_data):
        print(f"Starting process for iteration {i + 1} of {clone}")
        main_process(driver=None, variables=variables)  # Replace driver=None with actual driver instance if needed
        print(f"Completed process for iteration {i + 1} of {clone}")

    print(f"Completed process for {clone}")
    
    # Clear iterations_data for the next clone
    iterations_data.clear()